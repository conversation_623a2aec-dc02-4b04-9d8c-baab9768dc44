@echo off
REM 创新工坊预约系统 - Docker环境恢复脚本 (Windows版本)
REM 用于恢复MySQL数据库和应用数据

setlocal enabledelayedexpansion

REM 配置变量
set BACKUP_DIR=backup
set MYSQL_USER=root
set MYSQL_PASSWORD=123456
set PROJECT_NAME=newinno

echo.
echo ========================================
echo 创新工坊预约系统 - Docker恢复工具
echo ========================================
echo 恢复时间: %date% %time%
echo.

REM 检查参数
if "%1"=="" (
    echo [ERROR] 请指定备份文件的日期前缀
    echo.
    echo 用法: %0 [日期前缀]
    echo 示例: %0 20240101_120000
    echo.
    echo 可用的备份文件：
    echo ----------------------------------------
    if exist "%BACKUP_DIR%\reservation_system_*.sql" (
        for %%f in ("%BACKUP_DIR%\reservation_system_*.sql") do (
            set "filename=%%~nf"
            set "datepart=!filename:reservation_system_=!"
            echo   !datepart!
        )
    ) else (
        echo   没有找到备份文件
    )
    echo ----------------------------------------
    echo.
    pause
    exit /b 1
)

set BACKUP_PREFIX=%1

REM 检查Docker Compose是否运行
echo [INFO] 检查Docker Compose服务状态...
docker-compose ps >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Compose服务未运行，请先启动服务：
    echo [ERROR] docker-compose up -d
    pause
    exit /b 1
)

REM 检查容器是否运行
docker-compose ps | findstr "Up" >nul
if errorlevel 1 (
    echo [ERROR] 容器未正常运行，请检查服务状态
    pause
    exit /b 1
)

echo [INFO] Docker服务运行正常

REM 检查备份文件是否存在
echo [INFO] 检查备份文件...

if not exist "%BACKUP_DIR%\reservation_system_%BACKUP_PREFIX%.sql" (
    echo [ERROR] 主数据库备份文件不存在: %BACKUP_DIR%\reservation_system_%BACKUP_PREFIX%.sql
    pause
    exit /b 1
)

if not exist "%BACKUP_DIR%\admin_system_%BACKUP_PREFIX%.sql" (
    echo [ERROR] 管理数据库备份文件不存在: %BACKUP_DIR%\admin_system_%BACKUP_PREFIX%.sql
    pause
    exit /b 1
)

if not exist "%BACKUP_DIR%\app_data_%BACKUP_PREFIX%.tar.gz" (
    echo [ERROR] 应用数据备份文件不存在: %BACKUP_DIR%\app_data_%BACKUP_PREFIX%.tar.gz
    pause
    exit /b 1
)

echo [INFO] 备份文件检查通过

echo.
echo ========================================
echo ⚠️  警告：恢复操作将覆盖现有数据！
echo ========================================
echo 备份前缀: %BACKUP_PREFIX%
echo.
echo 将要恢复的文件：
echo   - MySQL数据库: reservation_system_%BACKUP_PREFIX%.sql
echo   - MySQL数据库: admin_system_%BACKUP_PREFIX%.sql
echo   - 应用数据: app_data_%BACKUP_PREFIX%.tar.gz
echo.

set /p choice="确认继续恢复操作？(Y/N): "
if /i not "%choice%"=="Y" (
    echo [INFO] 恢复操作已取消
    pause
    exit /b 0
)

echo.
echo [INFO] 开始恢复MySQL数据库...

REM 恢复主数据库
echo [INFO] 恢复主数据库 reservation_system...
docker-compose exec -T mysql mysql -u %MYSQL_USER% -p%MYSQL_PASSWORD% -e "DROP DATABASE IF EXISTS reservation_system; CREATE DATABASE reservation_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
if errorlevel 1 (
    echo [ERROR] 创建主数据库失败
    pause
    exit /b 1
)

docker-compose exec -T mysql mysql -u %MYSQL_USER% -p%MYSQL_PASSWORD% reservation_system < "%BACKUP_DIR%\reservation_system_%BACKUP_PREFIX%.sql"
if errorlevel 1 (
    echo [ERROR] 恢复主数据库失败
    pause
    exit /b 1
)

echo [INFO] 主数据库恢复完成

REM 恢复管理数据库
echo [INFO] 恢复管理数据库 admin_system...
docker-compose exec -T mysql mysql -u %MYSQL_USER% -p%MYSQL_PASSWORD% -e "DROP DATABASE IF EXISTS admin_system; CREATE DATABASE admin_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
if errorlevel 1 (
    echo [ERROR] 创建管理数据库失败
    pause
    exit /b 1
)

docker-compose exec -T mysql mysql -u %MYSQL_USER% -p%MYSQL_PASSWORD% admin_system < "%BACKUP_DIR%\admin_system_%BACKUP_PREFIX%.sql"
if errorlevel 1 (
    echo [ERROR] 恢复管理数据库失败
    pause
    exit /b 1
)

echo [INFO] 管理数据库恢复完成

echo.
echo [INFO] 开始恢复应用数据...

REM 停止应用服务
echo [INFO] 停止应用服务...
docker-compose stop reservation-system
if errorlevel 1 (
    echo [ERROR] 停止应用服务失败
    pause
    exit /b 1
)

REM 恢复应用数据
echo [INFO] 恢复应用数据卷...
docker run --rm -v %PROJECT_NAME%_app-data:/app -v "%cd%\%BACKUP_DIR%":/backup alpine sh -c "cd /app && rm -rf * && tar xzf /backup/app_data_%BACKUP_PREFIX%.tar.gz"
if errorlevel 1 (
    echo [ERROR] 恢复应用数据失败
    pause
    exit /b 1
)

REM 重启应用服务
echo [INFO] 重启应用服务...
docker-compose start reservation-system
if errorlevel 1 (
    echo [ERROR] 重启应用服务失败
    pause
    exit /b 1
)

echo [INFO] 应用数据恢复完成

echo.
echo [INFO] 验证恢复结果...

REM 等待服务启动
echo [INFO] 等待服务启动（10秒）...
timeout /t 10 /nobreak >nul

REM 检查数据库连接
docker-compose exec mysql mysql -u %MYSQL_USER% -p%MYSQL_PASSWORD% -e "SHOW DATABASES;" >nul 2>&1
if errorlevel 1 (
    echo [ERROR] ✗ MySQL数据库连接失败
) else (
    echo [INFO] ✓ MySQL数据库连接正常
)

REM 检查API服务
curl -f http://localhost:8001/health >nul 2>&1
if errorlevel 1 (
    echo [WARN] ⚠ API服务可能未完全启动，请稍后检查
) else (
    echo [INFO] ✓ API服务运行正常
)

REM 检查管理系统
curl -f http://localhost:5000 >nul 2>&1
if errorlevel 1 (
    echo [WARN] ⚠ 管理系统可能未完全启动，请稍后检查
) else (
    echo [INFO] ✓ 管理系统运行正常
)

echo.
echo ========================================
echo 恢复任务完成！
echo ========================================
echo 恢复时间: %date% %time%
echo 备份前缀: %BACKUP_PREFIX%
echo.
echo 请检查系统功能是否正常：
echo   - 管理系统：http://localhost:5000
echo   - API服务：http://localhost:8001
echo   - API文档：http://localhost:8001/docs
echo.

REM 询问是否打开浏览器
set /p choice="是否打开浏览器检查系统？(Y/N): "
if /i "%choice%"=="Y" (
    start http://localhost:5000
    start http://localhost:8001/docs
)

echo.
echo 恢复完成说明：
echo 1. 数据库已恢复到备份时的状态
echo 2. 应用数据已恢复到备份时的状态
echo 3. 如有问题，请检查Docker容器日志：docker-compose logs -f
echo.
pause
