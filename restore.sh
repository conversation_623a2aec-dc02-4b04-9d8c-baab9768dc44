#!/bin/bash

# 创新工坊预约系统 - Docker环境恢复脚本
# 用于恢复MySQL数据库和应用数据

set -e  # 遇到错误立即退出

# 配置变量
BACKUP_DIR="./backup"
MYSQL_USER="root"
MYSQL_PASSWORD="123456"
PROJECT_NAME="newinno"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "创新工坊预约系统 - Docker恢复脚本"
    echo ""
    echo "用法: $0 [选项] [备份文件前缀]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示帮助信息"
    echo "  -d, --dir DIR           指定备份目录 (默认: ./backup)"
    echo "  -l, --list              列出可用的备份文件"
    echo "  -m, --mysql-only        只恢复MySQL数据库"
    echo "  -a, --app-only          只恢复应用数据"
    echo "  -f, --force             强制恢复，不询问确认"
    echo ""
    echo "参数:"
    echo "  备份文件前缀            指定要恢复的备份文件日期前缀"
    echo "                         格式: YYYYMMDD_HHMMSS"
    echo ""
    echo "示例:"
    echo "  $0 -l                           # 列出可用备份"
    echo "  $0 20240101_120000              # 恢复指定时间的备份"
    echo "  $0 -m 20240101_120000           # 只恢复MySQL数据库"
    echo "  $0 -a 20240101_120000           # 只恢复应用数据"
    echo "  $0 -f 20240101_120000           # 强制恢复，不询问"
}

# 检查Docker Compose是否运行
check_docker_compose() {
    if ! docker-compose ps | grep -q "Up"; then
        log_error "Docker Compose服务未运行，请先启动服务："
        log_error "docker-compose up -d"
        exit 1
    fi
}

# 列出可用的备份文件
list_backups() {
    log_info "可用的备份文件："
    echo "----------------------------------------"
    
    if [ ! -d "$BACKUP_DIR" ]; then
        log_warn "备份目录不存在: $BACKUP_DIR"
        return 1
    fi
    
    # 查找备份文件并按时间排序
    find "$BACKUP_DIR" -name "reservation_system_*.sql" | sort -r | while read -r file; do
        if [ -f "$file" ]; then
            basename=$(basename "$file")
            date_part=$(echo "$basename" | sed 's/reservation_system_\(.*\)\.sql/\1/')
            formatted_date=$(echo "$date_part" | sed 's/\([0-9]\{8\}\)_\([0-9]\{2\}\)\([0-9]\{2\}\)\([0-9]\{2\}\)/\1 \2:\3:\4/')
            file_size=$(du -h "$file" | cut -f1)
            echo "  $date_part  ($formatted_date)  [$file_size]"
        fi
    done
    
    echo "----------------------------------------"
    log_info "使用备份文件前缀（如：20240101_120000）来恢复数据"
}

# 确认恢复操作
confirm_restore() {
    local backup_prefix="$1"
    
    if [ "$FORCE_RESTORE" = "true" ]; then
        return 0
    fi
    
    log_warn "⚠️  警告：恢复操作将覆盖现有数据！"
    log_warn "备份前缀: $backup_prefix"
    echo ""
    echo "将要恢复的文件："
    
    if [ "$MYSQL_ONLY" != "true" ] && [ "$APP_ONLY" != "true" ]; then
        echo "  - MySQL数据库: reservation_system_${backup_prefix}.sql"
        echo "  - MySQL数据库: admin_system_${backup_prefix}.sql"
        echo "  - 应用数据: app_data_${backup_prefix}.tar.gz"
    elif [ "$MYSQL_ONLY" = "true" ]; then
        echo "  - MySQL数据库: reservation_system_${backup_prefix}.sql"
        echo "  - MySQL数据库: admin_system_${backup_prefix}.sql"
    elif [ "$APP_ONLY" = "true" ]; then
        echo "  - 应用数据: app_data_${backup_prefix}.tar.gz"
    fi
    
    echo ""
    read -p "确认继续恢复操作？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "恢复操作已取消"
        exit 0
    fi
}

# 检查备份文件是否存在
check_backup_files() {
    local backup_prefix="$1"
    local missing_files=()
    
    if [ "$MYSQL_ONLY" != "true" ] && [ "$APP_ONLY" != "true" ]; then
        # 检查所有文件
        [ ! -f "$BACKUP_DIR/reservation_system_${backup_prefix}.sql" ] && missing_files+=("reservation_system_${backup_prefix}.sql")
        [ ! -f "$BACKUP_DIR/admin_system_${backup_prefix}.sql" ] && missing_files+=("admin_system_${backup_prefix}.sql")
        [ ! -f "$BACKUP_DIR/app_data_${backup_prefix}.tar.gz" ] && missing_files+=("app_data_${backup_prefix}.tar.gz")
    elif [ "$MYSQL_ONLY" = "true" ]; then
        # 只检查MySQL文件
        [ ! -f "$BACKUP_DIR/reservation_system_${backup_prefix}.sql" ] && missing_files+=("reservation_system_${backup_prefix}.sql")
        [ ! -f "$BACKUP_DIR/admin_system_${backup_prefix}.sql" ] && missing_files+=("admin_system_${backup_prefix}.sql")
    elif [ "$APP_ONLY" = "true" ]; then
        # 只检查应用数据文件
        [ ! -f "$BACKUP_DIR/app_data_${backup_prefix}.tar.gz" ] && missing_files+=("app_data_${backup_prefix}.tar.gz")
    fi
    
    if [ ${#missing_files[@]} -gt 0 ]; then
        log_error "以下备份文件不存在："
        for file in "${missing_files[@]}"; do
            log_error "  - $BACKUP_DIR/$file"
        done
        exit 1
    fi
}

# 恢复MySQL数据库
restore_mysql() {
    local backup_prefix="$1"
    
    log_info "开始恢复MySQL数据库..."
    
    # 恢复主数据库
    log_info "恢复主数据库 reservation_system..."
    docker-compose exec -T mysql mysql -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" \
        -e "DROP DATABASE IF EXISTS reservation_system; CREATE DATABASE reservation_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    
    docker-compose exec -T mysql mysql -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" reservation_system \
        < "$BACKUP_DIR/reservation_system_${backup_prefix}.sql"
    
    # 恢复管理数据库
    log_info "恢复管理数据库 admin_system..."
    docker-compose exec -T mysql mysql -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" \
        -e "DROP DATABASE IF EXISTS admin_system; CREATE DATABASE admin_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    
    docker-compose exec -T mysql mysql -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" admin_system \
        < "$BACKUP_DIR/admin_system_${backup_prefix}.sql"
    
    log_info "MySQL数据库恢复完成"
}

# 恢复应用数据
restore_app_data() {
    local backup_prefix="$1"
    
    log_info "开始恢复应用数据..."
    
    # 停止应用服务
    log_info "停止应用服务..."
    docker-compose stop reservation-system
    
    # 恢复应用数据
    log_info "恢复应用数据卷..."
    docker run --rm \
        -v "${PROJECT_NAME}_app-data:/app" \
        -v "$(pwd)/$BACKUP_DIR:/backup" \
        alpine sh -c "cd /app && rm -rf * && tar xzf /backup/app_data_${backup_prefix}.tar.gz"
    
    # 重启应用服务
    log_info "重启应用服务..."
    docker-compose start reservation-system
    
    log_info "应用数据恢复完成"
}

# 验证恢复结果
verify_restore() {
    log_info "验证恢复结果..."
    
    # 等待服务启动
    sleep 10
    
    # 检查数据库连接
    if docker-compose exec mysql mysql -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" -e "SHOW DATABASES;" > /dev/null 2>&1; then
        log_info "✓ MySQL数据库连接正常"
    else
        log_error "✗ MySQL数据库连接失败"
    fi
    
    # 检查应用服务
    if curl -f http://localhost:8001/health > /dev/null 2>&1; then
        log_info "✓ API服务运行正常"
    else
        log_warn "⚠ API服务可能未完全启动，请稍后检查"
    fi
    
    if curl -f http://localhost:5000 > /dev/null 2>&1; then
        log_info "✓ 管理系统运行正常"
    else
        log_warn "⚠ 管理系统可能未完全启动，请稍后检查"
    fi
}

# 主函数
main() {
    local backup_prefix="$1"
    
    if [ -z "$backup_prefix" ]; then
        log_error "请指定备份文件前缀"
        show_help
        exit 1
    fi
    
    log_info "开始恢复创新工坊预约系统..."
    log_info "恢复时间: $(date)"
    log_info "备份前缀: $backup_prefix"
    
    # 检查环境
    check_docker_compose
    
    # 检查备份文件
    check_backup_files "$backup_prefix"
    
    # 确认恢复操作
    confirm_restore "$backup_prefix"
    
    # 执行恢复
    if [ "$APP_ONLY" != "true" ]; then
        restore_mysql "$backup_prefix"
    fi
    
    if [ "$MYSQL_ONLY" != "true" ]; then
        restore_app_data "$backup_prefix"
    fi
    
    # 验证恢复结果
    verify_restore
    
    log_info "恢复任务完成！"
    log_info "请检查系统功能是否正常"
}

# 初始化变量
MYSQL_ONLY="false"
APP_ONLY="false"
FORCE_RESTORE="false"
LIST_BACKUPS="false"

# 处理命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -d|--dir)
            BACKUP_DIR="$2"
            shift 2
            ;;
        -l|--list)
            LIST_BACKUPS="true"
            shift
            ;;
        -m|--mysql-only)
            MYSQL_ONLY="true"
            shift
            ;;
        -a|--app-only)
            APP_ONLY="true"
            shift
            ;;
        -f|--force)
            FORCE_RESTORE="true"
            shift
            ;;
        -*)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
        *)
            BACKUP_PREFIX="$1"
            shift
            ;;
    esac
done

# 检查互斥选项
if [ "$MYSQL_ONLY" = "true" ] && [ "$APP_ONLY" = "true" ]; then
    log_error "--mysql-only 和 --app-only 选项不能同时使用"
    exit 1
fi

# 执行操作
if [ "$LIST_BACKUPS" = "true" ]; then
    list_backups
else
    main "$BACKUP_PREFIX"
fi
