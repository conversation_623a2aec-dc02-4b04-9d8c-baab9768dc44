@echo off
echo 创新工坊预约系统 - 文档清理脚本
echo ================================
echo.
echo 此脚本将清理已整理完成的旧文档目录：
echo - docs_archive/
echo - docs_old_backup/
echo - doc_old_backup/
echo - image/
echo.
echo 警告：此操作不可逆！请确保文档已正确迁移到docs/目录。
echo.
set /p confirm=确认执行清理操作？(y/N): 

if /i "%confirm%"=="y" (
    echo.
    echo 开始清理旧文档目录...
    
    if exist "docs_archive" (
        echo 删除 docs_archive/ 目录...
        rmdir /s /q "docs_archive"
        echo ✅ docs_archive/ 已删除
    ) else (
        echo ℹ️ docs_archive/ 目录不存在
    )
    
    if exist "docs_old_backup" (
        echo 删除 docs_old_backup/ 目录...
        rmdir /s /q "docs_old_backup"
        echo ✅ docs_old_backup/ 已删除
    ) else (
        echo ℹ️ docs_old_backup/ 目录不存在
    )
    
    if exist "doc_old_backup" (
        echo 删除 doc_old_backup/ 目录...
        rmdir /s /q "doc_old_backup"
        echo ✅ doc_old_backup/ 已删除
    ) else (
        echo ℹ️ doc_old_backup/ 目录不存在
    )
    
    if exist "image" (
        echo 删除 image/ 目录...
        rmdir /s /q "image"
        echo ✅ image/ 已删除
    ) else (
        echo ℹ️ image/ 目录不存在
    )
    
    echo.
    echo 🎉 文档清理完成！
    echo 所有文档现在统一管理在 docs/ 目录中。
) else (
    echo.
    echo 清理操作已取消。
)

echo.
pause
