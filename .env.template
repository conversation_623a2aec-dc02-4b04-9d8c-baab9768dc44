# 创新工坊预约系统 - 环境变量配置模板
# 复制此文件为 .env 并填入实际值

# ===========================================
# JWT 认证配置 (必须配置)
# ===========================================
# JWT密钥 - 使用以下命令生成: python -c "import secrets; print(secrets.token_hex(32))"
JWT_SECRET_KEY=your_jwt_secret_key_here_replace_with_actual_key

# JWT算法
JWT_ALGORITHM=HS256

# Token过期时间(分钟)
JWT_EXPIRE_MINUTES=30

# ===========================================
# AI服务配置 (可选)
# ===========================================
# DeepSeek API密钥 - 从 https://platform.deepseek.com 获取
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# DeepSeek API基础URL
DEEPSEEK_BASE_URL=https://api.deepseek.com

# 使用的模型
DEEPSEEK_MODEL=deepseek-chat

# ===========================================
# 数据库配置 (必须配置)
# ===========================================
# MySQL主机地址
MYSQL_HOST=localhost

# MySQL端口
MYSQL_PORT=3306

# MySQL用户名
MYSQL_USER=root

# MySQL密码 - 建议使用复杂密码
MYSQL_PASSWORD=your_secure_mysql_password_here

# 主数据库名称
MYSQL_DATABASE=reservation_system

# 管理系统数据库名称
ADMIN_MYSQL_DATABASE=admin_system

# ===========================================
# 应用配置
# ===========================================
# 应用密钥 - 用于Flask会话加密
SECRET_KEY=your_flask_secret_key_here

# 调试模式 (生产环境设为false)
DEBUG=false

# 环境类型 (development/staging/production)
ENVIRONMENT=production

# ===========================================
# CORS配置 (必须配置)
# ===========================================
# 允许的域名列表，用逗号分隔
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5000,https://yourdomain.com

# ===========================================
# 安全配置
# ===========================================
# API限流配置
RATE_LIMIT_ENABLED=true
LOGIN_RATE_LIMIT=5/minute
API_RATE_LIMIT=100/minute

# 文件上传限制
MAX_FILE_SIZE=10485760  # 10MB in bytes
ALLOWED_FILE_TYPES=.xlsx,.xls

# 密码策略
MIN_PASSWORD_LENGTH=6
REQUIRE_COMPLEX_PASSWORD=false

# ===========================================
# 日志配置
# ===========================================
# 日志级别 (DEBUG/INFO/WARNING/ERROR)
LOG_LEVEL=INFO

# 安全日志启用
SECURITY_LOGGING=true

# 日志文件路径
LOG_FILE_PATH=logs/app.log
SECURITY_LOG_PATH=logs/security.log

# ===========================================
# 邮件配置 (可选)
# ===========================================
# SMTP服务器配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_email_password
SMTP_USE_TLS=true

# 系统邮箱
SYSTEM_EMAIL=<EMAIL>

# ===========================================
# 监控配置 (可选)
# ===========================================
# 性能监控
ENABLE_METRICS=false
METRICS_PORT=9090

# 健康检查
HEALTH_CHECK_ENABLED=true

# ===========================================
# 缓存配置 (可选)
# ===========================================
# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 缓存过期时间(秒)
CACHE_EXPIRE_TIME=3600

# ===========================================
# 备份配置 (可选)
# ===========================================
# 自动备份启用
AUTO_BACKUP_ENABLED=false

# 备份间隔(小时)
BACKUP_INTERVAL=24

# 备份保留天数
BACKUP_RETENTION_DAYS=30

# 备份存储路径
BACKUP_PATH=backup/

# ===========================================
# 第三方服务配置 (可选)
# ===========================================
# 对象存储配置 (如阿里云OSS)
OSS_ACCESS_KEY_ID=
OSS_ACCESS_KEY_SECRET=
OSS_BUCKET_NAME=
OSS_ENDPOINT=

# 短信服务配置
SMS_ACCESS_KEY=
SMS_SECRET_KEY=
SMS_SIGN_NAME=

# ===========================================
# 开发配置 (仅开发环境)
# ===========================================
# 开发模式下的特殊配置
DEV_SKIP_AUTH=false
DEV_MOCK_DATA=false
DEV_SQL_ECHO=false

# 测试数据库配置
TEST_DATABASE_URL=sqlite:///test.db

# ===========================================
# 配置说明
# ===========================================
# 1. 所有包含 "your_" 前缀的值都必须替换为实际值
# 2. 密码和密钥建议使用强随机字符串
# 3. 生产环境务必设置 DEBUG=false
# 4. ALLOWED_ORIGINS 必须设置为实际的前端域名
# 5. 定期轮换密钥和密码
# 6. 不要将此文件提交到版本控制系统

# ===========================================
# 密钥生成命令参考
# ===========================================
# JWT密钥: python -c "import secrets; print(secrets.token_hex(32))"
# Flask密钥: python -c "import secrets; print(secrets.token_urlsafe(32))"
# MySQL密码: openssl rand -base64 32
# 随机字符串: python -c "import string, secrets; print(''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(32)))"
