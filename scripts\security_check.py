#!/usr/bin/env python3
"""
创新工坊预约系统 - 安全检查脚本
用于检查系统的安全配置和潜在漏洞
"""

import os
import re
import json
import subprocess
import sys
from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime

class SecurityChecker:
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.issues = []
        self.warnings = []
        self.info = []
        
    def log_issue(self, severity: str, category: str, message: str, file_path: str = "", line_number: int = 0):
        """记录安全问题"""
        issue = {
            "severity": severity,
            "category": category,
            "message": message,
            "file": file_path,
            "line": line_number,
            "timestamp": datetime.now().isoformat()
        }
        
        if severity == "HIGH":
            self.issues.append(issue)
        elif severity == "MEDIUM":
            self.warnings.append(issue)
        else:
            self.info.append(issue)
    
    def check_environment_variables(self):
        """检查环境变量配置"""
        print("🔍 检查环境变量配置...")
        
        env_file = self.project_root / ".env"
        if not env_file.exists():
            self.log_issue("HIGH", "配置", ".env文件不存在，请从.env.template复制并配置")
            return
        
        with open(env_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查必需的环境变量
        required_vars = [
            "JWT_SECRET_KEY",
            "MYSQL_PASSWORD",
            "ALLOWED_ORIGINS"
        ]
        
        for var in required_vars:
            if var not in content:
                self.log_issue("HIGH", "配置", f"缺少必需的环境变量: {var}")
            elif f"{var}=your_" in content or f"{var}=replace_" in content:
                self.log_issue("HIGH", "配置", f"环境变量 {var} 使用默认值，需要设置实际值")
        
        # 检查弱密码
        weak_patterns = [
            (r"PASSWORD=123456", "使用弱密码 123456"),
            (r"SECRET_KEY=your-secret-key", "使用默认密钥"),
            (r"JWT_SECRET_KEY=your_jwt_secret", "使用默认JWT密钥"),
        ]
        
        for pattern, message in weak_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                self.log_issue("HIGH", "密码安全", message)
        
        # 检查CORS配置
        if "ALLOWED_ORIGINS=*" in content:
            self.log_issue("HIGH", "CORS安全", "CORS配置允许所有域名，存在安全风险")
        
        print("✅ 环境变量检查完成")
    
    def check_hardcoded_secrets(self):
        """检查硬编码的敏感信息"""
        print("🔍 检查硬编码敏感信息...")
        
        # 要检查的文件类型
        file_patterns = ["*.py", "*.js", "*.json", "*.yml", "*.yaml"]
        
        # 敏感信息模式
        secret_patterns = [
            (r'SECRET_KEY\s*=\s*["\'](?!.*getenv)[^"\']{8,}["\']', "硬编码密钥"),
            (r'password\s*=\s*["\'][^"\']{3,}["\']', "硬编码密码"),
            (r'api_key\s*=\s*["\'][^"\']{10,}["\']', "硬编码API密钥"),
            (r'sk-[a-zA-Z0-9]{32,}', "可能的API密钥"),
            (r'["\'][0-9a-f]{32,}["\']', "可能的哈希值或密钥"),
        ]
        
        for pattern_glob in file_patterns:
            for file_path in self.project_root.rglob(pattern_glob):
                # 跳过特定目录
                if any(part in str(file_path) for part in ['.git', '__pycache__', 'node_modules', '.env']):
                    continue
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        lines = content.split('\n')
                    
                    for i, line in enumerate(lines, 1):
                        for pattern, description in secret_patterns:
                            if re.search(pattern, line, re.IGNORECASE):
                                self.log_issue("MEDIUM", "硬编码敏感信息", 
                                             f"{description}: {line.strip()}", 
                                             str(file_path.relative_to(self.project_root)), i)
                
                except (UnicodeDecodeError, PermissionError):
                    continue
        
        print("✅ 硬编码敏感信息检查完成")
    
    def check_sql_injection(self):
        """检查SQL注入风险"""
        print("🔍 检查SQL注入风险...")
        
        # SQL注入风险模式
        sql_patterns = [
            (r'\.execute\s*\(\s*f["\']', "使用f-string构建SQL查询"),
            (r'\.execute\s*\(\s*["\'].*\{\}.*["\']', "使用字符串格式化构建SQL"),
            (r'\.execute\s*\(\s*.*\+.*\)', "使用字符串拼接构建SQL"),
            (r'text\s*\(\s*f["\']', "使用f-string构建原生SQL"),
        ]
        
        for py_file in self.project_root.rglob("*.py"):
            if '__pycache__' in str(py_file):
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                for i, line in enumerate(lines, 1):
                    for pattern, description in sql_patterns:
                        if re.search(pattern, line):
                            self.log_issue("MEDIUM", "SQL注入", 
                                         f"{description}: {line.strip()}", 
                                         str(py_file.relative_to(self.project_root)), i)
            
            except (UnicodeDecodeError, PermissionError):
                continue
        
        print("✅ SQL注入检查完成")
    
    def check_cors_configuration(self):
        """检查CORS配置"""
        print("🔍 检查CORS配置...")
        
        main_py = self.project_root / "app" / "main.py"
        if main_py.exists():
            with open(main_py, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if 'allow_origins=["*"]' in content:
                self.log_issue("HIGH", "CORS安全", "CORS配置允许所有域名访问")
            
            if 'allow_methods=["*"]' in content:
                self.log_issue("MEDIUM", "CORS安全", "CORS配置允许所有HTTP方法")
            
            if 'allow_headers=["*"]' in content:
                self.log_issue("MEDIUM", "CORS安全", "CORS配置允许所有请求头")
        
        print("✅ CORS配置检查完成")
    
    def check_file_permissions(self):
        """检查文件权限"""
        print("🔍 检查文件权限...")
        
        sensitive_files = [".env", "config.py", "settings.py"]
        
        for file_name in sensitive_files:
            for file_path in self.project_root.rglob(file_name):
                try:
                    stat = file_path.stat()
                    mode = oct(stat.st_mode)[-3:]
                    
                    # 检查是否对其他用户可读
                    if mode[2] in ['4', '5', '6', '7']:
                        self.log_issue("MEDIUM", "文件权限", 
                                     f"敏感文件对其他用户可读: {file_path} (权限: {mode})")
                
                except OSError:
                    continue
        
        print("✅ 文件权限检查完成")
    
    def check_dependencies(self):
        """检查依赖安全"""
        print("🔍 检查依赖安全...")
        
        requirements_file = self.project_root / "requirements.txt"
        if requirements_file.exists():
            try:
                # 使用safety检查已知漏洞
                result = subprocess.run(
                    ["safety", "check", "-r", str(requirements_file), "--json"],
                    capture_output=True, text=True, timeout=30
                )
                
                if result.returncode == 0:
                    self.log_issue("LOW", "依赖安全", "所有依赖包安全检查通过")
                else:
                    try:
                        vulnerabilities = json.loads(result.stdout)
                        for vuln in vulnerabilities:
                            self.log_issue("HIGH", "依赖安全", 
                                         f"发现漏洞: {vuln.get('package_name')} {vuln.get('installed_version')} - {vuln.get('advisory')}")
                    except json.JSONDecodeError:
                        self.log_issue("MEDIUM", "依赖安全", "无法解析安全扫描结果")
            
            except (subprocess.TimeoutExpired, FileNotFoundError):
                self.log_issue("LOW", "依赖安全", "无法运行safety检查，请安装: pip install safety")
        
        print("✅ 依赖安全检查完成")
    
    def check_debug_settings(self):
        """检查调试设置"""
        print("🔍 检查调试设置...")
        
        debug_patterns = [
            (r'DEBUG\s*=\s*True', "调试模式已启用"),
            (r'echo\s*=\s*True', "数据库调试日志已启用"),
            (r'print\s*\(.*password', "可能在打印密码信息"),
            (r'console\.log\s*\(.*password', "可能在控制台输出密码"),
        ]
        
        for py_file in self.project_root.rglob("*.py"):
            if '__pycache__' in str(py_file):
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                for i, line in enumerate(lines, 1):
                    for pattern, description in debug_patterns:
                        if re.search(pattern, line, re.IGNORECASE):
                            self.log_issue("MEDIUM", "调试设置", 
                                         f"{description}: {line.strip()}", 
                                         str(py_file.relative_to(self.project_root)), i)
            
            except (UnicodeDecodeError, PermissionError):
                continue
        
        print("✅ 调试设置检查完成")
    
    def generate_report(self):
        """生成安全报告"""
        print("\n📊 生成安全报告...")
        
        total_issues = len(self.issues) + len(self.warnings) + len(self.info)
        
        report = {
            "scan_time": datetime.now().isoformat(),
            "summary": {
                "total_issues": total_issues,
                "high_severity": len(self.issues),
                "medium_severity": len(self.warnings),
                "low_severity": len(self.info)
            },
            "issues": {
                "high": self.issues,
                "medium": self.warnings,
                "low": self.info
            }
        }
        
        # 保存JSON报告
        report_file = self.project_root / "security_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # 打印摘要
        print(f"\n🔒 安全检查完成!")
        print(f"📈 总计发现 {total_issues} 个问题:")
        print(f"   🚨 高风险: {len(self.issues)} 个")
        print(f"   ⚠️  中风险: {len(self.warnings)} 个")
        print(f"   💡 低风险: {len(self.info)} 个")
        
        if self.issues:
            print(f"\n🚨 高风险问题需要立即修复:")
            for issue in self.issues[:5]:  # 只显示前5个
                print(f"   - {issue['category']}: {issue['message']}")
                if issue['file']:
                    print(f"     文件: {issue['file']}:{issue['line']}")
        
        print(f"\n📄 详细报告已保存到: {report_file}")
        
        return len(self.issues) == 0  # 如果没有高风险问题返回True
    
    def run_all_checks(self):
        """运行所有安全检查"""
        print("🛡️  开始安全检查...")
        print("=" * 50)
        
        self.check_environment_variables()
        self.check_hardcoded_secrets()
        self.check_sql_injection()
        self.check_cors_configuration()
        self.check_file_permissions()
        self.check_dependencies()
        self.check_debug_settings()
        
        return self.generate_report()

def main():
    """主函数"""
    checker = SecurityChecker()
    
    try:
        success = checker.run_all_checks()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n❌ 安全检查被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 安全检查过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
