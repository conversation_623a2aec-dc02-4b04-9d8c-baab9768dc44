# 创新工坊预约系统 - 用户操作手册

## 1. 系统概述

创新工坊预约系统是一个集成了AI智能助手的综合性预约管理平台，包含微信小程序用户端和Web管理后台两个部分，支持场地预约、设备借用、3D打印机预约等多种功能。

### 1.1 系统架构
- **微信小程序端**：面向学生、教师的移动端应用
- **Web管理后台**：面向管理员的桌面端管理系统
- **AI智能助手**：基于DeepSeek模型的智能预约助手

### 1.2 用户角色
- **学生**：可进行预约申请、查看预约记录
- **教师**：具备学生权限，可审批部分预约
- **管理员**：拥有完整的系统管理权限

---

## 2. 微信小程序用户端操作指南

### 2.1 系统登录

#### 2.1.1 登录界面
用户首次使用需要进行登录认证。

**操作步骤：**
1. 打开微信小程序"创新工坊预约系统"
2. 点击底部导航栏"我的"选项
3. 在登录界面输入学号/工号和密码
4. 点击"登录"按钮完成认证

**界面元素：**
- 用户名输入框：输入学号或工号
- 密码输入框：输入登录密码
- 登录按钮：提交登录信息

**图片占位：** `登录界面截图.png`

#### 2.1.2 登录状态验证
系统会自动验证用户身份并保存登录状态。

**功能特点：**
- 自动保存登录状态，无需重复登录
- 支持角色识别（学生/教师/管理员）
- 登录失败时显示错误提示

### 2.2 首页功能

#### 2.2.1 首页布局
首页是系统的主要入口，展示所有可用的预约选项。

**界面组成：**
- 搜索框：快速搜索场地或设备
- 管理功能区：管理员专用功能入口
- 场地预约区：显示所有可用场地
- 设备预约区：显示所有可用设备
- 3D打印机预约：打印机预约入口
- 页脚信息：系统提示和说明

**图片占位：** `首页界面截图.png`

#### 2.2.2 搜索功能
支持对场地和设备进行实时搜索。

**操作方法：**
1. 点击顶部搜索框
2. 输入场地名称或设备名称
3. 系统自动过滤显示匹配结果
4. 点击清除按钮可清空搜索内容

**搜索特性：**
- 实时搜索，无需点击搜索按钮
- 支持模糊匹配
- 搜索结果实时更新

**图片占位：** `搜索功能截图.png`

#### 2.2.3 场地预约区域
动态显示所有可用场地，支持展开/收起操作。

**功能特点：**
- 动态加载场地列表
- 支持区域展开/收起
- 显示场地可用状态
- 点击场地名称进入预约页面

**图片占位：** `场地预约区域截图.png`

#### 2.2.4 设备预约区域
显示所有可借用的设备，默认收起状态。

**操作说明：**
- 点击"设备预约"标题展开设备列表
- 设备按钮显示设备名称
- 点击设备按钮进入设备预约页面
- 支持搜索过滤设备

**图片占位：** `设备预约区域截图.png`

### 2.3 场地预约功能

#### 2.3.1 场地预约页面
选择具体场地后进入预约表单页面。

**表单字段：**
- 场地类型：自动显示选择的场地类型
- 预约日期：日期选择器，限制最早为当天
- 时间段：上午、下午、晚上三个时段选择
- 使用目的：文本输入框，描述使用目的
- 附属设备：可选的辅助设备（大屏、投影仪、麦克风、笔记本）

**操作流程：**
1. 选择预约日期
2. 选择时间段（系统会显示已占用时段）
3. 填写使用目的
4. 选择需要的附属设备
5. 点击"提交预约"按钮

**图片占位：** `场地预约表单截图.png`

#### 2.3.2 时间段冲突检查
系统会自动检查时间段冲突并提示用户。

**冲突处理：**
- 已占用时段显示为灰色不可选
- 选择冲突时段时显示提示信息
- 建议用户选择其他可用时段

**图片占位：** `时间段冲突提示截图.png`

### 2.4 设备预约功能

#### 2.4.1 设备预约表单
设备预约支持现场使用和外借两种模式。

**表单内容：**
- 设备名称：自动显示选择的设备
- 使用类型：现场使用/外借选择
- 借用时间：日期时间选择器
- 归还时间：外借模式必填，现场使用可选
- 使用原因：文本描述使用目的
- 指导老师：可选填写指导老师姓名

**使用类型说明：**
- **现场使用**：在实验室内使用，无需归还时间
- **外借**：带出实验室使用，必须填写归还时间

**图片占位：** `设备预约表单截图.png`

#### 2.4.2 设备状态检查
系统会检查设备可用性和冲突情况。

**状态验证：**
- 检查设备是否可用
- 验证时间段是否冲突
- 显示设备当前状态

**图片占位：** `设备状态检查截图.png`

### 2.5 3D打印机预约

#### 2.5.1 打印机预约表单
专门的3D打印机预约功能。

**预约信息：**
- 打印机选择：从可用打印机列表中选择
- 预约日期：选择打印日期
- 开始时间：打印开始时间
- 结束时间：预计打印结束时间
- 预计时长：系统自动计算或手动输入
- 模型名称：打印模型的名称
- 使用目的：描述打印目的
- 指导老师：可选填写

**图片占位：** `3D打印机预约表单截图.png`

### 2.6 AI智能助手

#### 2.6.1 AI聊天界面
集成DeepSeek AI模型的智能助手功能。

**界面特点：**
- 可爱的猫咪头像设计
- 欢迎消息和功能介绍
- 消息气泡式对话界面
- 实时输入和发送功能

**图片占位：** `AI聊天界面截图.png`

#### 2.6.2 AI预约助手功能
AI助手可以帮助用户完成预约操作。

**AI功能：**
- 理解自然语言预约需求
- 智能识别时间表述（如"明天下午"）
- 推荐合适的场地和设备
- 协助收集预约信息
- 自动提交预约申请

**对话示例：**
- 用户："我想明天下午预约一个会议室"
- AI："好的，我来帮您查看明天下午的会议室可用情况..."

**图片占位：** `AI预约助手对话截图.png`

#### 2.6.3 AI功能控制
管理员可以控制AI功能的开启和关闭。

**控制机制：**
- AI功能关闭时，小程序中不显示AI助手选项
- 自定义TabBar动态调整
- 确保微信审核合规性

### 2.7 个人中心

#### 2.7.1 用户信息展示
显示当前登录用户的基本信息。

**信息内容：**
- 用户头像（默认头像）
- 用户姓名
- 用户角色（学生/教师/管理员）
- 所属部门

**图片占位：** `个人中心用户信息截图.png`

#### 2.7.2 功能菜单
提供各种功能的快速入口。

**菜单项目：**
- 预约记录：查看个人预约历史
- 管理后台：管理员和教师专用入口
- 退出登录：安全退出系统

**图片占位：** `个人中心功能菜单截图.png`

### 2.8 预约记录管理

#### 2.8.1 预约记录列表
显示用户的所有预约记录，支持分页和筛选。

**记录分类：**
- 全部记录：显示所有预约
- 待审批：等待审批的预约
- 已通过：审批通过的预约
- 已拒绝：被拒绝的预约

**记录信息：**
- 预约类型（场地/设备/打印机）
- 预约时间
- 预约状态
- 审批信息

**图片占位：** `预约记录列表截图.png`

#### 2.8.2 预约详情查看
点击预约记录可查看详细信息。

**详情内容：**
- 完整的预约信息
- 审批状态和审批人
- 时间戳信息
- 操作历史

**图片占位：** `预约详情页面截图.png`

#### 2.8.3 分页功能
支持大量数据的分页显示。

**分页特性：**
- 每页显示固定数量记录
- 上一页/下一页导航
- 页码显示和跳转
- 加载状态提示

**图片占位：** `分页功能截图.png`

### 2.9 审批管理（管理员功能）

#### 2.9.1 审批列表
管理员可以查看和处理所有待审批的预约。

**审批界面：**
- 预约类型标识
- 申请人信息
- 预约详情摘要
- 审批操作按钮

**操作功能：**
- 通过审批
- 拒绝申请
- 查看详情
- 批量操作

**图片占位：** `审批管理列表截图.png`

#### 2.9.2 审批详情
查看预约申请的完整信息。

**详情展示：**
- 申请人完整信息
- 预约详细内容
- 申请时间
- 审批历史

**图片占位：** `审批详情页面截图.png`

### 2.10 导航系统

#### 2.10.1 自定义TabBar
底部导航栏支持动态配置。

**导航选项：**
- 首页：系统主页面
- AI助手：智能助手功能（可控制显示/隐藏）
- 我的：个人中心

**动态特性：**
- 根据AI功能状态动态显示
- 选中状态高亮显示
- 图标和文字组合

**图片占位：** `自定义TabBar截图.png`

#### 2.10.2 页面导航
支持页面间的跳转和返回。

**导航功能：**
- 页面标题显示
- 返回按钮
- 面包屑导航（部分页面）

---

## 3. Web管理后台操作指南

### 3.1 管理员登录

#### 3.1.1 登录页面
管理后台的登录界面。

**登录信息：**
- 管理员用户名
- 管理员密码
- 登录按钮
- 记住登录状态选项

**安全特性：**
- 密码加密传输
- 登录状态保持
- 失败次数限制

**图片占位：** `管理后台登录页面截图.png`

### 3.2 管理控制台

#### 3.2.1 控制台首页
管理后台的主要功能入口。

**功能模块：**
- 用户管理：用户账号管理
- 预约管理：预约记录管理
- 设备和场地管理：资源管理
- 数据统计：统计分析

**界面特点：**
- 卡片式布局
- 图标和描述
- 快速入口链接

**图片占位：** `管理控制台首页截图.png`

#### 3.2.2 AI功能控制
管理AI助手功能的开启和关闭。

**控制界面：**
- AI功能状态显示
- 开关切换按钮
- 状态说明文字
- 操作提示信息

**功能说明：**
- 实时控制小程序AI功能
- 状态同步更新
- 操作日志记录

**图片占位：** `AI功能控制界面截图.png`

### 3.3 用户管理

#### 3.3.1 用户列表
管理系统中的所有用户账号。

**列表功能：**
- 用户信息展示（姓名、用户名、角色、部门）
- 搜索和筛选功能
- 分页显示
- 批量操作

**操作功能：**
- 添加新用户
- 编辑用户信息
- 删除用户
- 批量删除
- 角色切换

**图片占位：** `用户管理列表截图.png`

#### 3.3.2 用户搜索和筛选
支持多条件搜索和筛选用户。

**搜索功能：**
- 按姓名搜索
- 按用户名搜索
- 按部门筛选
- 按角色筛选

**筛选选项：**
- 全部用户
- 学生用户
- 教师用户
- 管理员用户

**图片占位：** `用户搜索筛选截图.png`

#### 3.3.3 用户批量导入
支持Excel文件批量导入用户。

**导入功能：**
- Excel模板下载
- 文件上传
- 数据验证
- 导入进度显示
- 错误报告

**导入流程：**
1. 下载用户导入模板
2. 填写用户信息
3. 上传Excel文件
4. 系统验证数据
5. 确认导入结果

**图片占位：** `用户批量导入截图.png`

#### 3.3.4 导入进度监控
实时显示用户导入的进度状态。

**进度功能：**
- 实时进度条
- 任务状态显示
- 成功/失败统计
- 错误详情查看

**状态类型：**
- 等待中
- 进行中
- 已完成
- 失败

**图片占位：** `导入进度监控截图.png`

#### 3.3.5 用户批量删除
支持选择多个用户进行批量删除。

**删除功能：**
- 多选用户
- 批量删除确认
- 删除进度显示
- 操作结果反馈

**安全措施：**
- 删除确认对话框
- 系统管理员保护
- 操作日志记录

**图片占位：** `用户批量删除截图.png`

### 3.4 预约管理

#### 3.4.1 预约记录列表
管理所有类型的预约记录。

**记录展示：**
- 预约类型（场地/设备/打印机）
- 申请人信息
- 预约时间
- 当前状态
- 审批信息

**状态类型：**
- 待审批
- 已通过
- 已拒绝
- 已完成
- 已归还

**图片占位：** `预约记录管理列表截图.png`

#### 3.4.2 高级筛选功能
支持多条件组合筛选预约记录。

**筛选条件：**
- 预约类型筛选
- 状态筛选
- 日期范围筛选
- 申请人筛选
- 审批人筛选

**筛选界面：**
- 下拉选择框
- 日期选择器
- 文本输入框
- 重置按钮

**图片占位：** `预约高级筛选截图.png`

#### 3.4.3 预约审批操作
管理员可以批量审批预约申请。

**审批功能：**
- 单个审批
- 批量审批
- 审批意见填写
- 状态更新

**审批选项：**
- 通过申请
- 拒绝申请
- 要求修改

**图片占位：** `预约审批操作截图.png`

#### 3.4.4 预约记录分页
支持大量预约记录的分页显示。

**分页特性：**
- 每页记录数设置
- 页码导航
- 快速跳转
- 总数统计

**性能优化：**
- 懒加载
- 数据缓存
- 异步加载

**图片占位：** `预约记录分页截图.png`

#### 3.4.5 预约数据导出
支持将预约数据导出为Excel文件。

**导出功能：**
- 全部数据导出
- 筛选结果导出
- 自定义字段选择
- 格式化输出

**导出格式：**
- Excel (.xlsx)
- 包含完整预约信息
- 状态和时间戳

**图片占位：** `预约数据导出截图.png`

### 3.5 资源管理

#### 3.5.1 设备和场地管理
管理系统中的所有可预约资源。

**资源类型：**
- 场地资源（会议室、讲座厅等）
- 设备资源（实验设备、工具等）
- 打印机资源

**管理功能：**
- 添加新资源
- 编辑资源信息
- 删除资源
- 状态管理

**图片占位：** `资源管理列表截图.png`

#### 3.5.2 资源添加和编辑
添加或修改资源信息。

**资源信息：**
- 资源名称
- 资源类型
- 数量设置
- 可用数量
- 状态设置

**状态选项：**
- 可用
- 维护中
- 停用

**图片占位：** `资源添加编辑截图.png`

#### 3.5.3 资源状态监控
实时监控资源的使用状态。

**监控信息：**
- 资源总数
- 可用数量
- 预约中数量
- 维护状态

**状态指示：**
- 颜色标识
- 状态图标
- 数量统计

**图片占位：** `资源状态监控截图.png`

### 3.6 数据统计

#### 3.6.1 统计面板
系统使用情况的综合统计。

**统计指标：**
- 用户总数
- 预约记录总数
- 今日预约数
- 本月预约数

**显示方式：**
- 数字卡片
- 趋势图表
- 百分比显示
- 同比增长

**图片占位：** `数据统计面板截图.png`

#### 3.6.2 预约趋势图表
显示预约数据的时间趋势。

**图表类型：**
- 折线图：预约趋势
- 柱状图：类型分布
- 饼图：状态分布
- 面积图：累计统计

**时间维度：**
- 日统计
- 周统计
- 月统计
- 年统计

**图片占位：** `预约趋势图表截图.png`

#### 3.6.3 类型分布统计
不同预约类型的使用情况统计。

**分布统计：**
- 场地预约占比
- 设备预约占比
- 打印机预约占比
- 各类型详细数据

**可视化展示：**
- 饼图显示
- 数据表格
- 百分比标识
- 数量对比

**图片占位：** `类型分布统计截图.png`

#### 3.6.4 用户活跃度分析
分析用户的使用活跃度。

**活跃度指标：**
- 活跃用户数
- 预约频次
- 使用时长
- 部门分布

**分析维度：**
- 按部门统计
- 按角色统计
- 按时间统计
- 按活跃度排名

**图片占位：** `用户活跃度分析截图.png`

---

## 4. 系统特色功能

### 4.1 重复提交防护
系统具备智能的重复提交防护机制。

**防护机制：**
- 基于用户ID和请求数据的哈希校验
- 短时间内相同请求自动拦截
- 用户友好的提示信息
- 防止网络延迟导致的重复操作

**图片占位：** `重复提交防护提示截图.png`

### 4.2 时区处理
系统正确处理时区问题，确保时间显示准确。

**时区特性：**
- 自动识别UTC+8中国时区
- 统一的时间戳处理
- 跨平台时间同步
- 夏令时自动调整

### 4.3 数据验证
完善的数据验证机制保证数据质量。

**验证层次：**
- 前端实时验证
- 后端业务逻辑验证
- 数据库约束验证
- 格式和类型检查

### 4.4 错误处理
友好的错误处理和用户提示。

**错误处理：**
- 统一的错误信息格式
- 用户友好的提示文字
- 详细的错误日志记录
- 自动重试机制

**图片占位：** `错误处理提示截图.png`

---

## 5. 系统安全与权限

### 5.1 用户认证
基于JWT的安全认证机制。

**认证特性：**
- 加密的用户凭证
- 会话状态管理
- 自动登录过期
- 安全的密码存储

### 5.2 权限控制
基于角色的权限管理系统。

**权限层级：**
- **学生**：基础预约功能
- **教师**：预约功能 + 部分审批权限
- **管理员**：完整的系统管理权限

**权限验证：**
- 前端界面权限控制
- 后端API权限验证
- 数据访问权限限制
- 操作日志记录

### 5.3 数据安全
多层次的数据安全保护。

**安全措施：**
- 数据传输加密
- 敏感信息脱敏
- 定期数据备份
- 访问日志审计

---

## 6. 性能优化

### 6.1 前端优化
微信小程序的性能优化措施。

**优化策略：**
- 图片懒加载
- 数据分页加载
- 缓存机制
- 异步数据请求

### 6.2 后端优化
服务器端的性能优化。

**优化措施：**
- 数据库查询优化
- 索引设计优化
- 连接池管理
- 缓存策略

### 6.3 数据库优化
数据库层面的性能提升。

**优化方案：**
- 合理的表结构设计
- 索引优化
- 查询语句优化
- 分页查询实现

---

## 7. 移动端适配

### 7.1 响应式设计
适配不同尺寸的移动设备。

**适配特性：**
- 弹性布局设计
- 字体大小自适应
- 触摸操作优化
- 横竖屏适配

### 7.2 用户体验优化
针对移动端的用户体验优化。

**体验优化：**
- 简洁的界面设计
- 直观的操作流程
- 快速的响应速度
- 友好的交互反馈

**图片占位：** `移动端界面适配截图.png`

---

## 8. 系统维护

### 8.1 日志管理
完善的系统日志记录。

**日志类型：**
- 用户操作日志
- 系统错误日志
- 性能监控日志
- 安全审计日志

### 8.2 数据备份
定期的数据备份机制。

**备份策略：**
- 自动定时备份
- 增量备份
- 异地备份存储
- 快速恢复机制

### 8.3 系统监控
实时的系统状态监控。

**监控指标：**
- 系统性能指标
- 用户访问统计
- 错误率监控
- 资源使用情况

---

## 9. 常见问题解答

### 9.1 登录问题
**Q: 忘记密码怎么办？**
A: 请联系系统管理员重置密码。

**Q: 登录后显示权限不足？**
A: 请确认您的账号角色设置是否正确。

### 9.2 预约问题
**Q: 为什么无法预约某个时间段？**
A: 该时间段可能已被其他用户预约，请选择其他可用时段。

**Q: 预约提交后多久能得到审批结果？**
A: 通常在1-2个工作日内完成审批，紧急情况可联系管理员。

### 9.3 AI助手问题
**Q: AI助手无法使用？**
A: 请检查网络连接，或联系管理员确认AI功能是否已启用。

**Q: AI助手理解错误怎么办？**
A: 可以重新描述需求，或使用传统的预约方式。

### 9.4 技术问题
**Q: 小程序加载缓慢？**
A: 请检查网络连接，或尝试重启小程序。

**Q: 数据显示不正确？**
A: 请尝试下拉刷新页面，或联系技术支持。

---

## 10. 联系支持

### 10.1 技术支持
- **邮箱**：<EMAIL>
- **电话**：400-123-4567
- **工作时间**：周一至周五 9:00-17:00

### 10.2 用户反馈
- **功能建议**：<EMAIL>
- **问题报告**：<EMAIL>

### 10.3 系统更新
- 系统会定期发布更新版本
- 重要更新会通过系统通知用户
- 更新日志可在管理后台查看

---

## 11. 附录

### 11.1 系统要求
**微信小程序端：**
- 微信版本：7.0.0 及以上
- 系统要求：iOS 10.0+ / Android 6.0+
- 网络要求：稳定的网络连接

**Web管理后台：**
- 浏览器：Chrome 70+、Firefox 65+、Safari 12+
- 分辨率：1280x720 及以上
- 网络要求：稳定的网络连接

### 11.2 术语解释
- **预约**：用户申请使用场地或设备的行为
- **审批**：管理员对预约申请进行审核的过程
- **AI助手**：基于人工智能的智能预约助手
- **TabBar**：小程序底部导航栏
- **JWT**：JSON Web Token，用于用户认证的技术

### 11.3 版本信息
- **当前版本**：V2.0
- **发布日期**：2025年5月
- **主要特性**：AI智能助手、容器化部署、数据统计
- **兼容性**：向下兼容V1.0数据

---

**文档版本**：V2.0
**最后更新**：2025年5月30日
**文档状态**：正式版

---

*本操作手册详细介绍了创新工坊预约系统的所有功能和操作方法，涵盖了微信小程序用户端和Web管理后台的完整使用流程。如有疑问，请联系技术支持团队。*
