# 安全部署检查清单

## 📋 部署前安全检查

### 🔐 认证与授权

- [ ] **JWT密钥配置**
  - [ ] JWT_SECRET_KEY已设置为强随机密钥
  - [ ] 密钥长度至少32字符
  - [ ] 未使用默认或示例密钥
  - [ ] Token过期时间设置合理(建议30分钟)

- [ ] **用户认证**
  - [ ] 密码使用bcrypt加密存储
  - [ ] 登录失败次数限制已启用
  - [ ] 会话管理安全配置
  - [ ] 管理员权限验证正确

### 🌐 网络安全

- [ ] **CORS配置**
  - [ ] ALLOWED_ORIGINS设置为具体域名
  - [ ] 不使用通配符"*"
  - [ ] 允许的HTTP方法已限制
  - [ ] 允许的请求头已限制

- [ ] **API安全**
  - [ ] API限流已启用
  - [ ] 敏感接口需要认证
  - [ ] 输入验证已实施
  - [ ] 输出编码防XSS

### 🗄️ 数据库安全

- [ ] **连接安全**
  - [ ] 数据库密码复杂且唯一
  - [ ] 连接使用加密传输
  - [ ] 数据库端口未暴露到外网
  - [ ] 连接池配置合理

- [ ] **查询安全**
  - [ ] 使用参数化查询
  - [ ] 避免动态SQL拼接
  - [ ] 数据库用户权限最小化
  - [ ] 敏感数据加密存储

### 📁 文件安全

- [ ] **文件上传**
  - [ ] 文件类型白名单验证
  - [ ] 文件大小限制
  - [ ] 文件内容扫描
  - [ ] 上传路径安全控制

- [ ] **文件权限**
  - [ ] 敏感文件权限正确(600或644)
  - [ ] .env文件不可公开访问
  - [ ] 日志文件权限安全
  - [ ] 备份文件权限控制

### 🔧 配置安全

- [ ] **环境变量**
  - [ ] 所有敏感信息使用环境变量
  - [ ] .env文件在.gitignore中
  - [ ] 生产环境配置独立
  - [ ] 配置文件权限安全

- [ ] **调试设置**
  - [ ] DEBUG模式已关闭
  - [ ] SQL日志在生产环境关闭
  - [ ] 错误信息不泄露敏感信息
  - [ ] 开发工具已移除

## 🚀 部署过程检查

### 📦 容器安全

- [ ] **Docker配置**
  - [ ] 使用非root用户运行
  - [ ] 镜像来源可信
  - [ ] 最小化镜像内容
  - [ ] 定期更新基础镜像

- [ ] **网络隔离**
  - [ ] 容器网络正确配置
  - [ ] 不必要的端口未暴露
  - [ ] 服务间通信加密
  - [ ] 防火墙规则正确

### 🔍 依赖安全

- [ ] **包管理**
  - [ ] 依赖包版本固定
  - [ ] 已知漏洞扫描通过
  - [ ] 不必要的依赖已移除
  - [ ] 定期更新安全补丁

- [ ] **第三方服务**
  - [ ] API密钥安全存储
  - [ ] 服务访问权限最小化
  - [ ] 通信使用HTTPS
  - [ ] 定期轮换密钥

## 📊 部署后验证

### 🧪 功能测试

- [ ] **认证测试**
  ```bash
  # 测试登录功能
  curl -X POST http://localhost:8001/api/token \
    -d "username=test&password=test" \
    -H "Content-Type: application/x-www-form-urlencoded"
  
  # 测试Token验证
  curl -X GET http://localhost:8001/api/users/me \
    -H "Authorization: Bearer YOUR_TOKEN"
  ```

- [ ] **权限测试**
  ```bash
  # 测试管理员接口
  curl -X GET http://localhost:8001/api/admin/users \
    -H "Authorization: Bearer ADMIN_TOKEN"
  
  # 测试普通用户访问限制
  curl -X GET http://localhost:8001/api/admin/users \
    -H "Authorization: Bearer USER_TOKEN"
  ```

### 🛡️ 安全测试

- [ ] **CORS测试**
  ```bash
  # 测试跨域限制
  curl -X OPTIONS http://localhost:8001/api/users/me \
    -H "Origin: http://malicious-site.com" \
    -H "Access-Control-Request-Method: GET"
  ```

- [ ] **限流测试**
  ```bash
  # 测试登录限流
  for i in {1..10}; do
    curl -X POST http://localhost:8001/api/token \
      -d "username=test&password=wrong" \
      -H "Content-Type: application/x-www-form-urlencoded"
  done
  ```

- [ ] **文件上传测试**
  ```bash
  # 测试文件类型限制
  curl -X POST http://localhost:8001/api/admin/users/import \
    -F "file=@malicious.exe" \
    -H "Authorization: Bearer ADMIN_TOKEN"
  
  # 测试文件大小限制
  curl -X POST http://localhost:8001/api/admin/users/import \
    -F "file=@large_file.xlsx" \
    -H "Authorization: Bearer ADMIN_TOKEN"
  ```

### 📈 监控验证

- [ ] **日志检查**
  - [ ] 应用日志正常记录
  - [ ] 安全日志正常工作
  - [ ] 错误日志不包含敏感信息
  - [ ] 日志轮转配置正确

- [ ] **性能监控**
  - [ ] 响应时间正常
  - [ ] 内存使用合理
  - [ ] 数据库连接正常
  - [ ] 磁盘空间充足

## 🔧 自动化检查脚本

### 运行安全检查

```bash
# 运行完整安全检查
python scripts/security_check.py

# 检查依赖漏洞
pip install safety
safety check -r requirements.txt

# 代码安全扫描
pip install bandit
bandit -r app/ -f json -o security_report.json

# Docker镜像安全扫描
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
  aquasec/trivy image reservation-system:latest
```

### 配置验证脚本

```bash
#!/bin/bash
# config_check.sh - 配置验证脚本

echo "🔍 检查配置安全..."

# 检查环境变量
if [ -z "$JWT_SECRET_KEY" ]; then
  echo "❌ JWT_SECRET_KEY 未设置"
  exit 1
fi

if [ "$DEBUG" = "true" ]; then
  echo "⚠️ 警告: DEBUG模式已启用"
fi

# 检查文件权限
if [ -f ".env" ]; then
  PERM=$(stat -c "%a" .env)
  if [ "$PERM" != "600" ] && [ "$PERM" != "644" ]; then
    echo "⚠️ 警告: .env文件权限不安全 ($PERM)"
  fi
fi

# 检查CORS配置
if grep -q "allow_origins=\[\"*\"\]" app/main.py; then
  echo "❌ CORS配置不安全"
  exit 1
fi

echo "✅ 配置检查通过"
```

## 📋 部署检查表

### 部署前检查 (Pre-deployment)

| 检查项目 | 状态 | 备注 |
|---------|------|------|
| 环境变量配置 | ⬜ | 所有敏感信息已配置 |
| CORS策略限制 | ⬜ | 域名白名单已设置 |
| 调试模式关闭 | ⬜ | DEBUG=false |
| 密钥强度验证 | ⬜ | 使用强随机密钥 |
| 依赖安全扫描 | ⬜ | 无已知漏洞 |
| 代码安全扫描 | ⬜ | 无安全问题 |
| 文件权限检查 | ⬜ | 敏感文件权限正确 |
| 容器安全配置 | ⬜ | Docker配置安全 |

### 部署后验证 (Post-deployment)

| 检查项目 | 状态 | 备注 |
|---------|------|------|
| 应用启动正常 | ⬜ | 所有服务运行 |
| 认证功能正常 | ⬜ | 登录/Token验证 |
| 权限控制有效 | ⬜ | 管理员权限验证 |
| API限流生效 | ⬜ | 限流规则工作 |
| CORS策略生效 | ⬜ | 跨域限制正确 |
| 文件上传限制 | ⬜ | 类型/大小限制 |
| 日志记录正常 | ⬜ | 安全日志工作 |
| 监控指标正常 | ⬜ | 性能指标正常 |

### 持续监控 (Ongoing)

| 监控项目 | 频率 | 负责人 |
|---------|------|--------|
| 安全日志审查 | 每日 | 运维团队 |
| 依赖漏洞扫描 | 每周 | 开发团队 |
| 配置安全检查 | 每月 | 安全团队 |
| 渗透测试 | 每季度 | 外部安全公司 |

## 🚨 应急响应

### 安全事件处理

1. **发现安全问题**
   - 立即隔离受影响系统
   - 收集和保存证据
   - 通知相关人员

2. **影响评估**
   - 确定影响范围
   - 评估数据泄露风险
   - 制定修复计划

3. **修复措施**
   - 实施紧急修复
   - 更新安全配置
   - 验证修复效果

4. **事后分析**
   - 分析根本原因
   - 更新安全策略
   - 改进检查流程

### 联系信息

- **安全负责人**: [安全团队邮箱]
- **技术负责人**: [技术团队邮箱]
- **应急热线**: [24小时联系电话]

---

**检查完成签名**

- 开发负责人: _________________ 日期: _________
- 安全负责人: _________________ 日期: _________
- 运维负责人: _________________ 日期: _________

*本检查清单应在每次部署前完成，并保留检查记录。*
