@echo off
REM 创新工坊预约系统 - Docker环境备份脚本 (Windows版本)
REM 用于备份MySQL数据库和应用数据

setlocal enabledelayedexpansion

REM 配置变量
set BACKUP_DIR=backup
set MYSQL_USER=root
set MYSQL_PASSWORD=123456
set PROJECT_NAME=newinno

REM 获取当前日期时间
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set DATE=%YYYY%%MM%%DD%_%HH%%Min%%Sec%

echo.
echo ========================================
echo 创新工坊预约系统 - Docker备份工具
echo ========================================
echo 备份时间: %date% %time%
echo.

REM 检查Docker Compose是否运行
echo [INFO] 检查Docker Compose服务状态...
docker-compose ps >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Compose服务未运行，请先启动服务：
    echo [ERROR] docker-compose up -d
    pause
    exit /b 1
)

REM 检查容器是否运行
docker-compose ps | findstr "Up" >nul
if errorlevel 1 (
    echo [ERROR] 容器未正常运行，请检查服务状态
    pause
    exit /b 1
)

echo [INFO] Docker服务运行正常

REM 创建备份目录
if not exist "%BACKUP_DIR%" (
    mkdir "%BACKUP_DIR%"
    echo [INFO] 创建备份目录: %BACKUP_DIR%
)

echo.
echo [INFO] 开始备份MySQL数据库...

REM 备份主数据库
echo [INFO] 备份主数据库 reservation_system...
docker-compose exec -T mysql mysqldump -u %MYSQL_USER% -p%MYSQL_PASSWORD% --single-transaction --routines --triggers reservation_system > "%BACKUP_DIR%\reservation_system_%DATE%.sql"
if errorlevel 1 (
    echo [ERROR] 主数据库备份失败
    pause
    exit /b 1
)

REM 备份管理数据库
echo [INFO] 备份管理数据库 admin_system...
docker-compose exec -T mysql mysqldump -u %MYSQL_USER% -p%MYSQL_PASSWORD% --single-transaction --routines --triggers admin_system > "%BACKUP_DIR%\admin_system_%DATE%.sql"
if errorlevel 1 (
    echo [ERROR] 管理数据库备份失败
    pause
    exit /b 1
)

REM 备份所有数据库
echo [INFO] 备份所有数据库...
docker-compose exec -T mysql mysqldump -u %MYSQL_USER% -p%MYSQL_PASSWORD% --all-databases --single-transaction --routines --triggers > "%BACKUP_DIR%\all_databases_%DATE%.sql"
if errorlevel 1 (
    echo [ERROR] 完整数据库备份失败
    pause
    exit /b 1
)

echo [INFO] MySQL数据库备份完成

echo.
echo [INFO] 开始备份应用数据...

REM 备份应用数据卷
docker run --rm -v %PROJECT_NAME%_app-data:/app -v "%cd%\%BACKUP_DIR%":/backup alpine tar czf /backup/app_data_%DATE%.tar.gz -C /app .
if errorlevel 1 (
    echo [ERROR] 应用数据备份失败
    pause
    exit /b 1
)

echo [INFO] 应用数据备份完成

echo.
echo [INFO] 备份配置文件...

REM 备份docker-compose.yml
if exist "docker-compose.yml" (
    copy "docker-compose.yml" "%BACKUP_DIR%\docker-compose_%DATE%.yml" >nul
    echo [INFO] 已备份 docker-compose.yml
)

REM 备份.env文件
if exist ".env" (
    copy ".env" "%BACKUP_DIR%\env_%DATE%.txt" >nul
    echo [WARN] 已备份 .env 文件，请妥善保管（包含敏感信息）
)

echo [INFO] 配置文件备份完成

echo.
echo [INFO] 清理7天前的备份文件...

REM 清理旧备份文件（Windows版本使用forfiles命令）
forfiles /p "%BACKUP_DIR%" /s /m *.sql /d -7 /c "cmd /c del @path" 2>nul
forfiles /p "%BACKUP_DIR%" /s /m *.tar.gz /d -7 /c "cmd /c del @path" 2>nul
forfiles /p "%BACKUP_DIR%" /s /m *.yml /d -7 /c "cmd /c del @path" 2>nul
forfiles /p "%BACKUP_DIR%" /s /m *.txt /d -7 /c "cmd /c del @path" 2>nul

echo [INFO] 旧备份文件清理完成

echo.
echo ========================================
echo 备份完成！备份文件信息：
echo ========================================
echo 备份时间: %date% %time%
echo 备份目录: %BACKUP_DIR%
echo.
echo 文件列表:
dir "%BACKUP_DIR%\*_%DATE%.*" /b 2>nul
echo.

REM 计算备份目录大小
for /f "usebackq" %%A in (`dir "%BACKUP_DIR%" /s /-c ^| find "个文件"`) do (
    set "size=%%A"
    set "size=!size:个文件=!"
    set "size=!size: =!"
    set "size=!size:,=!"
)

echo 备份目录大小: !size! 字节
echo ========================================
echo.
echo [INFO] 备份任务完成！
echo.

REM 询问是否查看备份目录
set /p choice="是否打开备份目录查看文件？(Y/N): "
if /i "%choice%"=="Y" (
    explorer "%BACKUP_DIR%"
)

echo.
echo 使用说明：
echo 1. 备份文件已保存到 %BACKUP_DIR% 目录
echo 2. 可以使用 restore.bat 脚本恢复数据
echo 3. 建议定期将备份文件复制到安全位置
echo.
pause
