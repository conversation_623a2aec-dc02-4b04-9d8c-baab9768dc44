#!/bin/bash

# 创新工坊预约系统 - Docker环境备份脚本
# 用于备份MySQL数据库和应用数据

set -e  # 遇到错误立即退出

# 配置变量
BACKUP_DIR="./backup"
DATE=$(date +%Y%m%d_%H%M%S)
MYSQL_USER="root"
MYSQL_PASSWORD="123456"
PROJECT_NAME="newinno"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker Compose是否运行
check_docker_compose() {
    if ! docker-compose ps | grep -q "Up"; then
        log_error "Docker Compose服务未运行，请先启动服务："
        log_error "docker-compose up -d"
        exit 1
    fi
}

# 创建备份目录
create_backup_dir() {
    if [ ! -d "$BACKUP_DIR" ]; then
        mkdir -p "$BACKUP_DIR"
        log_info "创建备份目录: $BACKUP_DIR"
    fi
}

# 备份MySQL数据库
backup_mysql() {
    log_info "开始备份MySQL数据库..."
    
    # 备份主数据库
    log_info "备份主数据库 reservation_system..."
    docker-compose exec -T mysql mysqldump \
        -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" \
        --single-transaction \
        --routines \
        --triggers \
        reservation_system > "$BACKUP_DIR/reservation_system_$DATE.sql"
    
    # 备份管理数据库
    log_info "备份管理数据库 admin_system..."
    docker-compose exec -T mysql mysqldump \
        -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" \
        --single-transaction \
        --routines \
        --triggers \
        admin_system > "$BACKUP_DIR/admin_system_$DATE.sql"
    
    # 备份所有数据库（包括系统数据库）
    log_info "备份所有数据库..."
    docker-compose exec -T mysql mysqldump \
        -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" \
        --all-databases \
        --single-transaction \
        --routines \
        --triggers > "$BACKUP_DIR/all_databases_$DATE.sql"
    
    log_info "MySQL数据库备份完成"
}

# 备份应用数据
backup_app_data() {
    log_info "开始备份应用数据..."
    
    # 备份应用数据卷
    docker run --rm \
        -v "${PROJECT_NAME}_app-data:/app" \
        -v "$(pwd)/$BACKUP_DIR:/backup" \
        alpine tar czf "/backup/app_data_$DATE.tar.gz" -C /app .
    
    log_info "应用数据备份完成"
}

# 备份配置文件
backup_config() {
    log_info "备份配置文件..."
    
    # 备份docker-compose.yml
    if [ -f "docker-compose.yml" ]; then
        cp "docker-compose.yml" "$BACKUP_DIR/docker-compose_$DATE.yml"
    fi
    
    # 备份.env文件（如果存在）
    if [ -f ".env" ]; then
        cp ".env" "$BACKUP_DIR/env_$DATE.txt"
        log_warn "注意：.env文件包含敏感信息，请妥善保管备份文件"
    fi
    
    log_info "配置文件备份完成"
}

# 压缩备份文件
compress_backup() {
    log_info "压缩备份文件..."
    
    cd "$BACKUP_DIR"
    tar czf "backup_complete_$DATE.tar.gz" \
        reservation_system_$DATE.sql \
        admin_system_$DATE.sql \
        all_databases_$DATE.sql \
        app_data_$DATE.tar.gz \
        docker-compose_$DATE.yml \
        env_$DATE.txt 2>/dev/null || true
    
    cd ..
    log_info "备份文件压缩完成: backup_complete_$DATE.tar.gz"
}

# 清理旧备份
cleanup_old_backups() {
    log_info "清理7天前的备份文件..."
    
    find "$BACKUP_DIR" -name "*.sql" -mtime +7 -delete 2>/dev/null || true
    find "$BACKUP_DIR" -name "*.tar.gz" -mtime +7 -delete 2>/dev/null || true
    find "$BACKUP_DIR" -name "*.yml" -mtime +7 -delete 2>/dev/null || true
    find "$BACKUP_DIR" -name "*.txt" -mtime +7 -delete 2>/dev/null || true
    
    log_info "旧备份文件清理完成"
}

# 显示备份信息
show_backup_info() {
    log_info "备份完成！备份文件信息："
    echo "----------------------------------------"
    echo "备份时间: $(date)"
    echo "备份目录: $BACKUP_DIR"
    echo "文件列表:"
    ls -lh "$BACKUP_DIR"/*_$DATE.* 2>/dev/null || true
    echo "----------------------------------------"
    
    # 计算备份文件大小
    BACKUP_SIZE=$(du -sh "$BACKUP_DIR" | cut -f1)
    log_info "备份总大小: $BACKUP_SIZE"
}

# 主函数
main() {
    log_info "开始创新工坊预约系统备份..."
    log_info "备份时间: $(date)"
    
    # 检查环境
    check_docker_compose
    
    # 创建备份目录
    create_backup_dir
    
    # 执行备份
    backup_mysql
    backup_app_data
    backup_config
    
    # 压缩备份
    compress_backup
    
    # 清理旧备份
    cleanup_old_backups
    
    # 显示备份信息
    show_backup_info
    
    log_info "备份任务完成！"
}

# 帮助信息
show_help() {
    echo "创新工坊预约系统 - Docker备份脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -d, --dir      指定备份目录 (默认: ./backup)"
    echo "  -c, --cleanup  只清理旧备份文件"
    echo ""
    echo "示例:"
    echo "  $0                    # 执行完整备份"
    echo "  $0 -d /path/to/backup # 指定备份目录"
    echo "  $0 -c                 # 只清理旧备份"
}

# 处理命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -d|--dir)
            BACKUP_DIR="$2"
            shift 2
            ;;
        -c|--cleanup)
            create_backup_dir
            cleanup_old_backups
            exit 0
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主函数
main
