# 🐳 Docker部署说明

本文档提供使用Docker部署创新工坊预约系统的详细说明，包括完整的容器化部署方案。

## 📋 目录

- [🏗️ 系统架构](#️-系统架构)
- [⚙️ 技术配置](#️-技术配置)
- [🚀 快速部署](#-快速部署)
- [📊 服务管理](#-服务管理)
- [💾 数据管理](#-数据管理)
- [🔧 故障排除](#-故障排除)
- [🔄 更新部署](#-更新部署)
- [🔒 安全建议](#-安全建议)
- [📦 镜像发布](#-镜像发布)

## 🏗️ 系统架构

### 容器组成

系统采用多容器架构，包含以下组件：

1. **MySQL数据库容器** (`reservation-mysql`)
   - 使用MySQL 8.0官方镜像
   - 提供主数据库和管理系统数据库
   - 数据持久化存储
   - 仅容器内部访问，不暴露外部端口

2. **应用服务容器** (`reservation-system`)
   - **FastAPI后端服务**：端口8001，提供RESTful API接口
   - **Flask管理系统**：端口5000，提供Web管理界面
   - 集成AI智能助手功能
   - 自动数据库初始化和管理员账号创建

### 网络架构

```
┌─────────────────┐    ┌─────────────────┐
│   用户/前端      │    │   管理员        │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          │ :8001                │ :5000
          │                      │
    ┌─────▼──────────────────────▼─────┐
    │     reservation-system          │
    │  ┌─────────────┐ ┌─────────────┐ │
    │  │  FastAPI    │ │   Flask     │ │
    │  │   后端      │ │  管理系统    │ │
    │  └─────────────┘ └─────────────┘ │
    └─────────────┬───────────────────┘
                  │ 内部网络
    ┌─────────────▼───────────────────┐
    │      reservation-mysql          │
    │        MySQL 8.0                │
    └─────────────────────────────────┘
```

## ⚙️ 技术配置

### 时区配置

系统已配置为使用中国时区（UTC+8），确保时间记录的准确性：
- 容器内部时区设置为 `Asia/Shanghai`
- 环境变量 `TZ=Asia/Shanghai` 确保应用程序使用正确时区
- 自动处理夏令时转换

### 数据库配置

- **主数据库**：`reservation_system` - 存储预约、用户、设备等业务数据
- **管理数据库**：`admin_system` - 存储管理员账号和系统配置
- **字符集**：UTF-8，支持中文和特殊字符
- **认证插件**：mysql_native_password，确保兼容性

### 环境要求

#### 系统要求
- **操作系统**：Linux、macOS、Windows 10/11
- **内存**：至少2GB可用内存
- **磁盘空间**：至少5GB可用空间
- **网络**：可访问Docker Hub和GitHub

#### 软件依赖
- **Docker**：版本20.10+
- **Docker Compose**：版本2.0+

#### 安装Docker

**Ubuntu/Debian:**
```bash
# 更新包索引
sudo apt-get update

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo apt-get install docker-compose-plugin

# 将用户添加到docker组（可选）
sudo usermod -aG docker $USER
```

**CentOS/RHEL:**
```bash
# 安装Docker
sudo yum install -y yum-utils
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
sudo yum install docker-ce docker-ce-cli containerd.io docker-compose-plugin

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker
```

**Windows:**
- 下载并安装 [Docker Desktop for Windows](https://www.docker.com/products/docker-desktop)
- 确保启用WSL2后端

**macOS:**
- 下载并安装 [Docker Desktop for Mac](https://www.docker.com/products/docker-desktop)

## 🚀 快速部署

### 步骤1：获取代码

```bash
# 克隆代码库
git clone https://github.com/zzyss-marker/newinno.git
cd newinno

# 或者下载ZIP包并解压
wget https://github.com/zzyss-marker/newinno/archive/main.zip
unzip main.zip
cd newinno-main
```

### 步骤2：配置环境变量

```bash
# 复制示例环境变量文件
cp .env.example .env

# 编辑.env文件，配置必要参数
nano .env  # 或使用其他编辑器
```

**重要配置项说明：**

```bash
# AI功能配置（必需，如果要使用AI助手）
DEEPSEEK_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
DEEPSEEK_BASE_URL=https://api.deepseek.com
DEEPSEEK_MODEL=deepseek-chat

# MySQL配置（Docker环境下会自动配置，无需修改）
MYSQL_HOST=mysql
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=123456
MYSQL_DATABASE=reservation_system
ADMIN_MYSQL_DATABASE=admin_system
```

> **💡 提示**:
> - 如果不使用AI功能，可以跳过DEEPSEEK相关配置
> - MySQL配置在Docker环境下会自动设置，无需手动修改
> - .env文件包含敏感信息，不会被包含在Docker镜像中

### 步骤3：一键部署

```bash
# 构建并启动所有服务
docker-compose up -d --build
```

**部署过程说明：**
1. 🔄 拉取MySQL 8.0镜像
2. 🏗️ 构建应用服务镜像
3. 🗄️ 创建数据库容器并初始化
4. 🚀 启动应用服务容器
5. 📊 自动创建数据库表结构
6. 👤 自动创建管理员账号

### 步骤4：验证部署

```bash
# 检查容器状态
docker-compose ps

# 查看启动日志
docker-compose logs -f

# 等待服务完全启动（约30-60秒）
```

**预期输出：**
```
NAME                   IMAGE                    COMMAND                  SERVICE             CREATED             STATUS              PORTS
reservation-mysql      mysql:8.0               "docker-entrypoint.s…"   mysql               2 minutes ago       Up 2 minutes        3306/tcp, 33060/tcp
reservation-system     newinno-reservation-system   "/bin/bash -c 'pytho…"   reservation-system   2 minutes ago       Up 2 minutes        0.0.0.0:5000->5000/tcp, 0.0.0.0:8001->8001/tcp
```

### 步骤5：访问服务

- **🖥️ 管理系统**: http://localhost:5000
  - 用户名：`admin`
  - 密码：`admin123`

- **🔌 API服务**: http://localhost:8001
  - API文档：http://localhost:8001/docs
  - 健康检查：http://localhost:8001/health

- **📱 微信小程序**: 使用微信开发者工具打开 `fore/` 目录

## 📊 服务管理

### 查看服务状态

```bash
# 查看所有容器状态
docker-compose ps

# 查看详细信息
docker-compose ps -a

# 查看资源使用情况
docker stats
```

### 日志管理

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f reservation-system
docker-compose logs -f mysql

# 查看最近的日志（最后100行）
docker-compose logs --tail=100 reservation-system

# 查看特定时间段的日志
docker-compose logs --since="2024-01-01T00:00:00" --until="2024-01-01T23:59:59"
```

### 服务控制

```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose stop

# 重启服务
docker-compose restart

# 停止并删除容器（保留数据卷）
docker-compose down

# 停止并删除容器和数据卷（⚠️ 会丢失数据）
docker-compose down -v

# 重新构建并启动
docker-compose up -d --build --force-recreate
```

### 进入容器

```bash
# 进入应用容器
docker-compose exec reservation-system bash

# 进入MySQL容器
docker-compose exec mysql mysql -u root -p

# 以root用户进入容器
docker-compose exec --user root reservation-system bash
```

## 💾 数据管理

### 数据持久化

系统使用Docker卷来持久化数据，确保容器重启后数据不丢失：

- **`mysql-data`**：MySQL数据库文件
  - 位置：`/var/lib/mysql`
  - 包含：所有数据库表和数据

- **`app-data`**：应用程序数据
  - 位置：`/app`
  - 包含：应用代码、配置文件、日志等

### 数据库结构

```
MySQL容器
├── reservation_system (主数据库)
│   ├── users (用户表)
│   ├── venues (场地表)
│   ├── devices (设备表)
│   ├── printers (打印机表)
│   ├── venue_reservations (场地预约表)
│   ├── device_reservations (设备预约表)
│   └── printer_reservations (打印机预约表)
└── admin_system (管理数据库)
    ├── admin_users (管理员表)
    └── system_settings (系统设置表)
```

### 备份数据

#### 自动备份脚本

**Linux/macOS 用户：**
```bash
# 使用提供的备份脚本
./backup.sh

# 或手动备份
#!/bin/bash
BACKUP_DIR="./backup"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份MySQL数据
docker-compose exec mysql mysqldump -u root -p123456 --all-databases > $BACKUP_DIR/mysql_backup_$DATE.sql

# 备份应用数据
docker run --rm -v newinno_app-data:/app -v $(pwd)/$BACKUP_DIR:/backup alpine tar czf /backup/app_data_$DATE.tar.gz -C /app .

echo "备份完成: $BACKUP_DIR"
```

**Windows 用户：**
```batch
# 使用提供的备份脚本
backup.bat

# 脚本功能：
# - 自动检查Docker服务状态
# - 备份MySQL数据库（主库和管理库）
# - 备份应用数据卷
# - 备份配置文件
# - 自动清理7天前的旧备份
# - 彩色日志输出和进度提示
```

#### 手动备份

**Linux/macOS：**
```bash
# 创建备份目录
mkdir -p backup

# 备份MySQL数据库
docker-compose exec mysql mysqldump -u root -p123456 reservation_system > backup/reservation_system_$(date +%Y%m%d).sql
docker-compose exec mysql mysqldump -u root -p123456 admin_system > backup/admin_system_$(date +%Y%m%d).sql

# 备份应用数据
docker run --rm -v newinno_app-data:/app -v $(pwd)/backup:/backup alpine tar czf /backup/app_data_$(date +%Y%m%d).tar.gz -C /app .
```

**Windows：**
```batch
# 创建备份目录
mkdir backup

# 获取日期
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set DATE=%YYYY%%MM%%DD%

# 备份MySQL数据库
docker-compose exec -T mysql mysqldump -u root -p123456 reservation_system > backup\reservation_system_%DATE%.sql
docker-compose exec -T mysql mysqldump -u root -p123456 admin_system > backup\admin_system_%DATE%.sql

# 备份应用数据
docker run --rm -v newinno_app-data:/app -v "%cd%\backup":/backup alpine tar czf /backup/app_data_%DATE%.tar.gz -C /app .
```

### 恢复数据

#### 自动恢复脚本

**Linux/macOS 用户：**
```bash
# 使用提供的恢复脚本
./restore.sh

# 列出可用备份
./restore.sh -l

# 恢复指定备份
./restore.sh 20240101_120000

# 只恢复MySQL数据库
./restore.sh -m 20240101_120000

# 只恢复应用数据
./restore.sh -a 20240101_120000
```

**Windows 用户：**
```batch
# 使用提供的恢复脚本
restore.bat 20240101_120000

# 脚本功能：
# - 自动检查Docker服务状态
# - 验证备份文件完整性
# - 安全确认提示
# - 自动恢复数据库和应用数据
# - 验证恢复结果
# - 友好的用户界面
```

#### 手动恢复

**恢复MySQL数据：**
```bash
# Linux/macOS
docker-compose exec -T mysql mysql -u root -p123456 reservation_system < backup/reservation_system_20240101.sql
docker-compose exec -T mysql mysql -u root -p123456 admin_system < backup/admin_system_20240101.sql

# Windows
docker-compose exec -T mysql mysql -u root -p123456 reservation_system < backup\reservation_system_20240101.sql
docker-compose exec -T mysql mysql -u root -p123456 admin_system < backup\admin_system_20240101.sql
```

**恢复应用数据：**
```bash
# 停止服务
docker-compose stop reservation-system

# Linux/macOS
docker run --rm -v newinno_app-data:/app -v $(pwd)/backup:/backup alpine tar xzf /backup/app_data_20240101.tar.gz -C /app

# Windows
docker run --rm -v newinno_app-data:/app -v "%cd%\backup":/backup alpine tar xzf /backup/app_data_20240101.tar.gz -C /app

# 重启服务
docker-compose start reservation-system
```

## 🔧 故障排除

### 常见问题诊断

#### 1. 服务无法启动

**症状**：容器启动失败或立即退出

**诊断步骤**：
```bash
# 查看容器状态
docker-compose ps

# 查看详细日志
docker-compose logs reservation-system

# 检查端口占用
netstat -tulpn | grep -E ':(5000|8001|3306)'
```

**常见原因及解决方案**：
- **端口被占用**：修改docker-compose.yml中的端口映射
- **权限问题**：确保Docker有足够权限访问项目目录
- **内存不足**：释放系统内存或增加虚拟内存

#### 2. 数据库连接失败

**症状**：应用无法连接到MySQL数据库

**诊断步骤**：
```bash
# 检查MySQL容器状态
docker-compose logs mysql

# 测试数据库连接
docker-compose exec mysql mysql -u root -p123456 -e "SHOW DATABASES;"

# 检查网络连接
docker-compose exec reservation-system ping mysql
```

**解决方案**：
```bash
# 重启MySQL容器
docker-compose restart mysql

# 如果数据损坏，重新初始化
docker-compose down -v
docker-compose up -d
```

#### 3. AI功能不可用

**症状**：AI助手无响应或报错

**诊断步骤**：
```bash
# 检查环境变量
docker-compose exec reservation-system env | grep DEEPSEEK

# 测试API连接
docker-compose exec reservation-system curl -H "Authorization: Bearer $DEEPSEEK_API_KEY" https://api.deepseek.com/v1/models
```

**解决方案**：
- 检查.env文件中的DEEPSEEK_API_KEY是否正确
- 确认网络可以访问DeepSeek API
- 在管理系统中检查AI功能开关状态

#### 4. 时区问题

**症状**：时间记录不准确

**诊断步骤**：
```bash
# 检查容器内时区
docker-compose exec reservation-system date
docker-compose exec reservation-system cat /etc/timezone

# 检查环境变量
docker-compose exec reservation-system env | grep TZ
```

**解决方案**：
```bash
# 重新构建容器（已包含时区修复）
docker-compose down
docker-compose up -d --build
```

#### 5. 性能问题

**症状**：响应缓慢或超时

**诊断步骤**：
```bash
# 查看资源使用情况
docker stats

# 查看系统负载
docker-compose exec reservation-system top

# 检查数据库性能
docker-compose exec mysql mysqladmin -u root -p123456 processlist
```

**解决方案**：
- 增加容器内存限制
- 优化数据库查询
- 考虑使用缓存

### 日志分析

#### 应用日志位置
```bash
# FastAPI日志
docker-compose logs reservation-system | grep "uvicorn"

# Flask管理系统日志
docker-compose logs reservation-system | grep "werkzeug"

# MySQL日志
docker-compose logs mysql
```

#### 错误日志关键词
- `ERROR`：应用错误
- `CRITICAL`：严重错误
- `Connection refused`：连接被拒绝
- `Permission denied`：权限问题
- `Out of memory`：内存不足

### 网络问题

#### 检查网络配置
```bash
# 查看Docker网络
docker network ls

# 检查网络详情
docker network inspect newinno_reservation-network

# 测试容器间连接
docker-compose exec reservation-system ping mysql
```

#### 防火墙配置
```bash
# Ubuntu/Debian
sudo ufw allow 5000
sudo ufw allow 8001

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=5000/tcp
sudo firewall-cmd --permanent --add-port=8001/tcp
sudo firewall-cmd --reload
```

## 🔄 更新部署

### 标准更新流程

```bash
# 1. 备份当前数据
./backup.sh  # 如果有备份脚本

# 2. 拉取最新代码
git pull origin main

# 3. 查看更新内容
git log --oneline -10

# 4. 停止服务
docker-compose down

# 5. 重新构建并启动
docker-compose up -d --build

# 6. 验证服务状态
docker-compose ps
docker-compose logs -f --tail=50
```

### 零停机更新（生产环境）

```bash
# 1. 构建新镜像
docker-compose build

# 2. 滚动更新
docker-compose up -d --no-deps reservation-system

# 3. 健康检查
curl -f http://localhost:8001/health || exit 1
curl -f http://localhost:5000 || exit 1
```

### 回滚操作

```bash
# 1. 查看镜像历史
docker images

# 2. 回滚到指定版本
git checkout <previous-commit>
docker-compose up -d --build

# 3. 或者使用备份恢复数据
# 参考数据管理章节的恢复步骤
```

## 🔒 安全建议

### 基础安全配置

#### 1. 修改默认密码
```bash
# 修改MySQL root密码
docker-compose exec mysql mysql -u root -p123456 -e "ALTER USER 'root'@'%' IDENTIFIED BY 'new_strong_password';"

# 更新docker-compose.yml中的密码配置
# 更新.env文件中的密码配置
```

#### 2. 网络安全
```bash
# 限制MySQL只能容器内访问（已配置）
# 在docker-compose.yml中注释掉MySQL的ports配置

# 使用防火墙限制访问
sudo ufw enable
sudo ufw allow from ***********/24 to any port 5000  # 仅允许内网访问管理系统
```

#### 3. 文件权限
```bash
# 设置敏感文件权限
chmod 600 .env
chmod 600 backup/*.sql

# 设置目录权限
chmod 755 backup/
```

### 生产环境安全

#### 1. HTTPS配置
```yaml
# 在docker-compose.yml中添加nginx反向代理
nginx:
  image: nginx:alpine
  ports:
    - "443:443"
    - "80:80"
  volumes:
    - ./nginx.conf:/etc/nginx/nginx.conf
    - ./ssl:/etc/nginx/ssl
  depends_on:
    - reservation-system
```

#### 2. 环境变量安全
```bash
# 使用Docker secrets（Docker Swarm）
echo "your_secret_key" | docker secret create deepseek_api_key -

# 或使用外部密钥管理系统
# 如HashiCorp Vault、AWS Secrets Manager等
```

#### 3. 容器安全
```yaml
# 在docker-compose.yml中添加安全配置
reservation-system:
  security_opt:
    - no-new-privileges:true
  read_only: true
  tmpfs:
    - /tmp
  user: "1000:1000"  # 非root用户运行
```

#### 4. 监控和审计
```bash
# 启用Docker日志驱动
# 在docker-compose.yml中配置
logging:
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
```

### 数据安全

#### 1. 定期备份
```bash
# 设置cron定时备份
0 2 * * * /path/to/backup.sh

# 异地备份
rsync -av backup/ user@remote-server:/backup/reservation-system/
```

#### 2. 数据加密
```bash
# 加密备份文件
gpg --symmetric --cipher-algo AES256 backup/mysql_backup_20240101.sql

# 加密Docker卷（需要额外配置）
```

## 📦 镜像发布

### 构建生产镜像

#### 1. 多阶段构建优化
```dockerfile
# Dockerfile.production
FROM python:3.9-slim as builder
WORKDIR /app
COPY requirements.txt .
RUN pip install --user -r requirements.txt

FROM python:3.9-slim
WORKDIR /app
COPY --from=builder /root/.local /root/.local
COPY . .
ENV PATH=/root/.local/bin:$PATH
CMD ["python", "runme.py"]
```

#### 2. 构建和标签
```bash
# 构建生产镜像
docker build -f Dockerfile.production -t reservation-system:latest .

# 添加版本标签
docker tag reservation-system:latest reservation-system:v2.0.0
docker tag reservation-system:latest your-registry/reservation-system:latest
```

### 推送到镜像仓库

#### 1. Docker Hub
```bash
# 登录Docker Hub
docker login

# 推送镜像
docker push your-username/reservation-system:latest
docker push your-username/reservation-system:v2.0.0
```

#### 2. 私有镜像仓库
```bash
# 登录私有仓库
docker login your-registry.com

# 推送到私有仓库
docker tag reservation-system:latest your-registry.com/reservation-system:latest
docker push your-registry.com/reservation-system:latest
```

#### 3. 镜像安全扫描
```bash
# 使用Docker Scout扫描
docker scout cves reservation-system:latest

# 使用Trivy扫描
trivy image reservation-system:latest
```

### 部署配置

#### 1. 生产环境docker-compose.yml
```yaml
version: '3.8'
services:
  reservation-system:
    image: your-registry/reservation-system:v2.0.0
    restart: unless-stopped
    environment:
      - NODE_ENV=production
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
```

#### 2. 环境配置管理
```bash
# 使用不同的环境配置文件
cp .env.production .env

# 或使用环境变量覆盖
export DEEPSEEK_API_KEY="production-key"
docker-compose up -d
```

> **🔐 安全提示**:
> - 生产环境镜像不应包含任何敏感信息
> - 使用.dockerignore排除敏感文件
> - 定期更新基础镜像以修复安全漏洞
> - 使用镜像签名验证完整性
> - 实施最小权限原则

## 🚀 性能优化

### 容器资源配置

#### 1. 内存优化
```yaml
# docker-compose.yml
services:
  reservation-system:
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
    environment:
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
```

#### 2. CPU优化
```yaml
services:
  reservation-system:
    deploy:
      resources:
        limits:
          cpus: '1.0'
        reservations:
          cpus: '0.5'
```

#### 3. 数据库性能调优
```yaml
mysql:
  command: >
    --default-authentication-plugin=mysql_native_password
    --innodb-buffer-pool-size=256M
    --innodb-log-file-size=64M
    --max-connections=200
    --query-cache-size=32M
```

### 应用性能优化

#### 1. Python应用优化
```bash
# 在Dockerfile中添加
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONHASHSEED=random

# 使用更快的JSON库
RUN pip install ujson

# 启用多进程
CMD ["gunicorn", "--workers", "4", "--worker-class", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8001", "app.main:app"]
```

#### 2. 数据库连接池
```python
# 在应用配置中
DATABASE_URL = "mysql+pymysql://user:pass@mysql/db?charset=utf8mb4&pool_size=20&max_overflow=30"
```

### 缓存策略

#### 1. Redis缓存
```yaml
# 添加Redis服务
redis:
  image: redis:7-alpine
  restart: always
  volumes:
    - redis-data:/data
  networks:
    - reservation-network

volumes:
  redis-data:
```

#### 2. 应用级缓存
```python
# 在应用中使用缓存
from functools import lru_cache

@lru_cache(maxsize=128)
def get_venues():
    # 缓存场地数据
    pass
```

## 📊 监控和日志

### 容器监控

#### 1. 健康检查
```yaml
services:
  reservation-system:
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
```

#### 2. 资源监控
```bash
# 实时监控容器资源使用
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"

# 监控脚本
#!/bin/bash
while true; do
    echo "=== $(date) ==="
    docker-compose ps
    docker stats --no-stream
    echo ""
    sleep 60
done
```

### 日志管理

#### 1. 日志配置
```yaml
services:
  reservation-system:
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"
        labels: "service=reservation-system"
```

#### 2. 日志聚合
```bash
# 使用ELK Stack进行日志聚合
# docker-compose.elk.yml
version: '3.8'
services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.5.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data

  logstash:
    image: docker.elastic.co/logstash/logstash:8.5.0
    volumes:
      - ./logstash.conf:/usr/share/logstash/pipeline/logstash.conf

  kibana:
    image: docker.elastic.co/kibana/kibana:8.5.0
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
```

### 性能监控

#### 1. Prometheus监控
```yaml
# 添加Prometheus监控
prometheus:
  image: prom/prometheus
  ports:
    - "9090:9090"
  volumes:
    - ./prometheus.yml:/etc/prometheus/prometheus.yml
    - prometheus-data:/prometheus

grafana:
  image: grafana/grafana
  ports:
    - "3000:3000"
  volumes:
    - grafana-data:/var/lib/grafana
  environment:
    - GF_SECURITY_ADMIN_PASSWORD=admin
```

#### 2. 应用指标
```python
# 在应用中添加指标收集
from prometheus_client import Counter, Histogram, generate_latest

REQUEST_COUNT = Counter('http_requests_total', 'Total HTTP requests', ['method', 'endpoint'])
REQUEST_LATENCY = Histogram('http_request_duration_seconds', 'HTTP request latency')

@app.middleware("http")
async def metrics_middleware(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    REQUEST_COUNT.labels(method=request.method, endpoint=request.url.path).inc()
    REQUEST_LATENCY.observe(time.time() - start_time)
    return response
```

## 🔧 最佳实践

### 开发环境

#### 1. 开发配置
```yaml
# docker-compose.dev.yml
version: '3.8'
services:
  reservation-system:
    build:
      context: .
      dockerfile: Dockerfile.dev
    volumes:
      - .:/app
      - /app/__pycache__
    environment:
      - DEBUG=1
      - RELOAD=1
    command: ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8001", "--reload"]
```

#### 2. 热重载开发
```dockerfile
# Dockerfile.dev
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
RUN pip install watchdog
COPY . .
CMD ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8001", "--reload"]
```

### 生产环境

#### 1. 多阶段构建
```dockerfile
# Dockerfile.production
FROM python:3.9-slim as builder
WORKDIR /app
COPY requirements.txt .
RUN pip install --user --no-cache-dir -r requirements.txt

FROM python:3.9-slim as runtime
WORKDIR /app
COPY --from=builder /root/.local /root/.local
ENV PATH=/root/.local/bin:$PATH
COPY . .
RUN useradd --create-home --shell /bin/bash app
USER app
CMD ["gunicorn", "--workers", "4", "--worker-class", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8001", "app.main:app"]
```

#### 2. 负载均衡
```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - reservation-system-1
      - reservation-system-2

  reservation-system-1:
    build: .
    environment:
      - INSTANCE_ID=1

  reservation-system-2:
    build: .
    environment:
      - INSTANCE_ID=2
```

### CI/CD集成

#### 1. GitHub Actions
```yaml
# .github/workflows/docker.yml
name: Docker Build and Push
on:
  push:
    branches: [main]
    tags: ['v*']

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build and push
        uses: docker/build-push-action@v4
        with:
          context: .
          file: ./Dockerfile.production
          push: true
          tags: |
            your-username/reservation-system:latest
            your-username/reservation-system:${{ github.sha }}
```

#### 2. 自动化测试
```yaml
# 在CI中添加测试
- name: Run tests
  run: |
    docker-compose -f docker-compose.test.yml up --build --abort-on-container-exit
    docker-compose -f docker-compose.test.yml down
```

### 安全扫描

#### 1. 镜像安全扫描
```bash
# 使用Trivy扫描
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
  -v $HOME/Library/Caches:/root/.cache/ \
  aquasec/trivy image reservation-system:latest

# 使用Snyk扫描
snyk container test reservation-system:latest
```

#### 2. 依赖安全检查
```bash
# Python依赖安全检查
pip install safety
safety check -r requirements.txt

# 在Dockerfile中添加
RUN pip install safety && safety check
```

## 📚 常用命令速查

### 基础操作
```bash
# 启动服务
docker-compose up -d

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 重新构建
docker-compose up -d --build
```

### 数据库操作
```bash
# 连接MySQL
docker-compose exec mysql mysql -u root -p

# 备份数据库
docker-compose exec mysql mysqldump -u root -p123456 reservation_system > backup.sql

# 恢复数据库
docker-compose exec -T mysql mysql -u root -p123456 reservation_system < backup.sql

# 查看数据库大小
docker-compose exec mysql mysql -u root -p123456 -e "SELECT table_schema AS 'Database', ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)' FROM information_schema.tables GROUP BY table_schema;"
```

### 容器管理
```bash
# 进入容器
docker-compose exec reservation-system bash

# 查看容器资源使用
docker stats

# 清理未使用的镜像
docker image prune -a

# 清理未使用的卷
docker volume prune

# 查看卷信息
docker volume ls
docker volume inspect newinno_mysql-data
```

### 网络调试
```bash
# 查看网络
docker network ls

# 检查网络连接
docker-compose exec reservation-system ping mysql

# 查看端口占用
netstat -tulpn | grep -E ':(5000|8001|3306)'

# 测试API连接
curl -f http://localhost:8001/health
curl -f http://localhost:5000
```

---

## 📞 技术支持

如果在Docker部署过程中遇到问题，请：

1. **查看日志**：`docker-compose logs -f`
2. **检查状态**：`docker-compose ps`
3. **查看资源**：`docker stats`
4. **参考故障排除**：本文档的故障排除章节
5. **提交Issue**：[GitHub Issues](https://github.com/zzyss-marker/newinno/issues)

**常见问题快速解决**：
- 端口占用：修改docker-compose.yml中的端口映射
- 内存不足：增加系统内存或调整容器内存限制
- 网络问题：检查防火墙设置和Docker网络配置
- 权限问题：确保Docker有足够权限访问项目目录

---

<div align="center">
  <p>🐳 <strong>Docker让部署变得简单！</strong></p>
  <p>📖 更多信息请参考主README文档</p>
</div>
