{"scan_time": "2025-06-14T15:50:29.992551", "summary": {"total_issues": 24, "high_severity": 4, "medium_severity": 19, "low_severity": 1}, "issues": {"high": [{"severity": "HIGH", "category": "配置", "message": "缺少必需的环境变量: JWT_SECRET_KEY", "file": "", "line": 0, "timestamp": "2025-06-14T15:50:29.554030"}, {"severity": "HIGH", "category": "配置", "message": "缺少必需的环境变量: ALLOWED_ORIGINS", "file": "", "line": 0, "timestamp": "2025-06-14T15:50:29.554030"}, {"severity": "HIGH", "category": "密码安全", "message": "使用弱密码 123456", "file": "", "line": 0, "timestamp": "2025-06-14T15:50:29.554030"}, {"severity": "HIGH", "category": "CORS安全", "message": "CORS配置允许所有域名访问", "file": "", "line": 0, "timestamp": "2025-06-14T15:50:29.839153"}], "medium": [{"severity": "MEDIUM", "category": "硬编码敏感信息", "message": "硬编码密码: password='admin123',  # 在生产环境中应当使用哈希密码", "file": "admin_system\\create_admin.py", "line": 57, "timestamp": "2025-06-14T15:50:29.559768"}, {"severity": "MEDIUM", "category": "硬编码敏感信息", "message": "硬编码密码: password=\"admin123\",  # 注意：实际使用时应该使用加密后的密码", "file": "app\\db\\init_db.py", "line": 25, "timestamp": "2025-06-14T15:50:29.628675"}, {"severity": "MEDIUM", "category": "硬编码敏感信息", "message": "硬编码API密钥: DEEPSEEK_API_KEY = \"dev_key_replace_in_production\"", "file": "app\\routers\\ai.py", "line": 31, "timestamp": "2025-06-14T15:50:29.635299"}, {"severity": "MEDIUM", "category": "硬编码敏感信息", "message": "硬编码密钥: SECRET_KEY = \"your-secret-key\"  # 在生产环境中应该使用环境变量", "file": "app\\routers\\auth.py", "line": 35, "timestamp": "2025-06-14T15:50:29.635299"}, {"severity": "MEDIUM", "category": "硬编码敏感信息", "message": "硬编码密钥: SECRET_KEY = \"your-secret-key\"  # 在生产环境中应该使用环境变量", "file": "app\\utils\\auth.py", "line": 12, "timestamp": "2025-06-14T15:50:29.640572"}, {"severity": "MEDIUM", "category": "SQL注入", "message": "使用f-string构建原生SQL: conn.execute(text(f\"CREATE DATABASE IF NOT EXISTS {MYSQL_DATABASE} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci\"))", "file": "app\\database.py", "line": 26, "timestamp": "2025-06-14T15:50:29.793917"}, {"severity": "MEDIUM", "category": "SQL注入", "message": "使用f-string构建原生SQL: conn.execute(text(f\"CREATE DATABASE IF NOT EXISTS {admin_db} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci\"))", "file": "app\\database.py", "line": 31, "timestamp": "2025-06-14T15:50:29.793917"}, {"severity": "MEDIUM", "category": "SQL注入", "message": "使用f-string构建原生SQL: conn.execute(text(f\"CREATE DATABASE IF NOT EXISTS {ADMIN_MYSQL_DATABASE} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci\"))", "file": "app\\db\\admin_system_db.py", "line": 25, "timestamp": "2025-06-14T15:50:29.826804"}, {"severity": "MEDIUM", "category": "CORS安全", "message": "CORS配置允许所有HTTP方法", "file": "", "line": 0, "timestamp": "2025-06-14T15:50:29.839153"}, {"severity": "MEDIUM", "category": "CORS安全", "message": "CORS配置允许所有请求头", "file": "", "line": 0, "timestamp": "2025-06-14T15:50:29.839153"}, {"severity": "MEDIUM", "category": "文件权限", "message": "敏感文件对其他用户可读: e:\\Desktop\\25创新工坊预约系统\\newinno\\.env (权限: 666)", "file": "", "line": 0, "timestamp": "2025-06-14T15:50:29.839153"}, {"severity": "MEDIUM", "category": "文件权限", "message": "敏感文件对其他用户可读: e:\\Desktop\\25创新工坊预约系统\\newinno\\admin_system\\app\\config.py (权限: 666)", "file": "", "line": 0, "timestamp": "2025-06-14T15:50:29.896631"}, {"severity": "MEDIUM", "category": "文件权限", "message": "敏感文件对其他用户可读: e:\\Desktop\\25创新工坊预约系统\\newinno\\app\\models\\settings.py (权限: 666)", "file": "", "line": 0, "timestamp": "2025-06-14T15:50:29.927475"}, {"severity": "MEDIUM", "category": "文件权限", "message": "敏感文件对其他用户可读: e:\\Desktop\\25创新工坊预约系统\\newinno\\app\\routers\\settings.py (权限: 666)", "file": "", "line": 0, "timestamp": "2025-06-14T15:50:29.927475"}, {"severity": "MEDIUM", "category": "调试设置", "message": "调试模式已启用: app.run(host='0.0.0.0', port=5000, debug=True, use_reloader=False)", "file": "runme.py", "line": 18, "timestamp": "2025-06-14T15:50:29.940991"}, {"severity": "MEDIUM", "category": "调试设置", "message": "调试模式已启用: app.run(host='0.0.0.0', debug=True, port=5000)  # 修改为监听所有网络接口", "file": "admin_system\\run.py", "line": 14, "timestamp": "2025-06-14T15:50:29.941994"}, {"severity": "MEDIUM", "category": "调试设置", "message": "数据库调试日志已启用: echo=True,  # 启用SQL查询日志", "file": "app\\database.py", "line": 50, "timestamp": "2025-06-14T15:50:29.941994"}, {"severity": "MEDIUM", "category": "调试设置", "message": "可能在打印密码信息: print(f\"  密码: testpassword\")", "file": "test\\create_test_user.py", "line": 52, "timestamp": "2025-06-14T15:50:29.945082"}, {"severity": "MEDIUM", "category": "调试设置", "message": "可能在打印密码信息: print(f\"Invalid password for user: {form_data.username}\")", "file": "app\\routers\\auth.py", "line": 72, "timestamp": "2025-06-14T15:50:29.980323"}], "low": [{"severity": "LOW", "category": "依赖安全", "message": "无法运行safety检查，请安装: pip install safety", "file": "", "line": 0, "timestamp": "2025-06-14T15:50:29.939991"}]}}